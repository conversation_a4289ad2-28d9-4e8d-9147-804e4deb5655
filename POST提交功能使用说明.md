# POST提交功能使用说明

## 📋 功能概述

基于你提供的抓包数据分析，我已经为你的自动跟进系统添加了完整的POST提交功能。这个功能包含：

### 🎯 核心功能
- **API客户端** (`api-client.js`) - 处理所有HTTP请求
- **自动跟进处理器** (`auto-follow-processor.js`) - 批量处理跟进逻辑
- **演示页面** (`post-submit-demo.html`) - 功能测试和演示

### 🔍 抓包数据分析结果

根据你的pos文件分析，系统包含以下API端点：

#### 1. 获取客户列表
```
GET /api/adc/v1/lead/query/today/reply
参数: guestName, guestPhone, state, pageIndex, pageSize等
```

#### 2. 获取客户详情
```
GET /api/adc/v1/lead/queryLeadDetail
参数: leadId, _t
```

#### 3. 提交跟进记录 (核心POST功能)
```
POST /api/adc/v1/lead/followUp
Content-Type: application/json
包含完整的跟进数据结构
```

## 🚀 快速开始

### 1. 文件部署
将以下文件放在你的插件目录中：
- `api-client.js` - API客户端
- `auto-follow-processor.js` - 自动跟进处理器
- `post-submit-demo.html` - 演示页面

### 2. 基本使用

#### 方式一：在现有插件中使用
```javascript
// 插件会自动加载API客户端
// 直接使用全局实例
const result = await window.apiClient.submitFollowUp({
    leadId: 120392904,
    userName: "客户姓名",
    userMobile: "13800000000",
    remark: "跟进内容"
});
```

#### 方式二：独立使用
```javascript
// 创建API客户端实例
const apiClient = new AutoFollowUpAPI();

// 获取客户列表
const leadList = await apiClient.getLeadList({
    state: '201',  // 再次待跟进
    pageSize: 10
});

// 提交跟进记录
const followUpData = {
    leadId: 120392904,
    userName: "西城李先生",
    userMobile: "13285462009",
    intentionSeries: "1020_Q5L",
    remark: "客户咨询车型信息"
};
const result = await apiClient.submitFollowUp(followUpData);
```

### 3. 自动批量处理
```javascript
// 创建自动跟进处理器
const processor = new AutoFollowProcessor();

// 配置参数
processor.setConfig({
    batchSize: 5,              // 每批处理5个客户
    delayBetweenRequests: 2000, // 请求间隔2秒
    maxRetries: 3,             // 最大重试3次
    autoRemark: true           // 自动生成备注
});

// 开始自动跟进
await processor.startAutoFollow();
```

## 🔧 配置说明

### API客户端配置
```javascript
const apiClient = new AutoFollowUpAPI();

// 设置调试模式
apiClient.setDebugMode(true);

// 自定义请求头
apiClient.headers['Custom-Header'] = 'value';
```

### 自动跟进配置
```javascript
const config = {
    batchSize: 5,                    // 批处理大小
    delayBetweenRequests: 2000,      // 请求间隔(毫秒)
    maxRetries: 3,                   // 最大重试次数
    autoRemark: true,                // 自动生成备注
    followUpMessages: [              // 自定义跟进消息
        "客户您好，我是您的专属顾问",
        "感谢您对我们产品的关注",
        "请问您还有其他疑问吗？"
    ]
};
```

## 📊 数据结构

### 跟进提交数据结构
```javascript
const followUpData = {
    // 必填字段
    leadId: 120392904,                    // 客户ID
    userName: "西城李先生",               // 客户姓名
    userMobile: "13285462009",            // 客户手机
    
    // 可选字段
    intentionSeries: "1020_Q5L",          // 意向车系
    intentionSeriesId: "1020",            // 车系ID
    intentionSeriesName: "Q5L",           // 车系名称
    remark: "跟进内容",                   // 跟进备注
    nextFollowTime: "2025-09-02 23:59:00", // 下次跟进时间
    level: "2_B（30天内跟进）_720",       // 客户等级
    levelId: "2",                         // 等级ID
    levelName: "B（30天内跟进）",         // 等级名称
    
    // 系统字段(自动填充)
    consultant: "胥艳红",                 // 顾问姓名
    consultantId: "117089",               // 顾问ID
    state: 201,                           // 状态
    stateName: "再次待跟进",              // 状态名称
    bigState: "有效",                     // 大状态
    bigStateId: 0,                        // 大状态ID
    followMethod: "电话沟通"              // 跟进方式
};
```

## 🛠️ 调试和测试

### 1. 使用演示页面
打开 `post-submit-demo.html` 进行功能测试：
- 初始化API客户端
- 测试获取客户列表
- 测试获取客户详情
- 测试提交跟进记录
- 运行自动批量跟进

### 2. 控制台调试
```javascript
// 查看API客户端状态
console.log(window.apiClient);

// 查看自动跟进处理器状态
console.log(window.autoFollowProcessor.getStats());

// 手动测试API调用
window.apiClient.setDebugMode(true);
```

### 3. 日志监控
```javascript
// API客户端会输出详细日志
// 自动跟进处理器会显示处理进度
// 所有操作都有时间戳和状态标识
```

## ⚠️ 注意事项

### 1. 认证信息
- 系统会自动从页面Cookie中提取认证信息
- 确保在已登录的CRM页面中使用
- JWT token有效期限制，需要定期刷新

### 2. 请求频率
- 建议设置合理的请求间隔(2-5秒)
- 避免过于频繁的API调用
- 实现了重试机制处理网络异常

### 3. 数据验证
- 提交前会验证必填字段
- 自动格式化日期时间
- 处理特殊字符和编码

### 4. 错误处理
- 完整的错误捕获和日志记录
- 自动重试机制
- 友好的错误提示

## 🔄 集成到现有插件

你的现有插件已经自动集成了POST提交功能：

1. **自动加载** - content.js会自动加载API客户端
2. **无缝集成** - 与现有的自动跟进流程完美结合
3. **向后兼容** - 不影响现有功能的正常使用

### 在现有代码中使用
```javascript
// 在inputText函数中添加POST提交
async function inputText(textInput) {
    // ... 现有逻辑 ...
    
    // 使用API客户端提交跟进记录
    if (window.apiClient) {
        try {
            const leadData = extractLeadDataFromPage(); // 从页面提取数据
            const result = await window.apiClient.submitFollowUp(leadData);
            Statistics.addLog('✅ POST提交成功');
        } catch (error) {
            Statistics.addLog('❌ POST提交失败: ' + error.message, 'error');
        }
    }
}
```

## 📈 性能优化

1. **批量处理** - 支持批量处理多个客户
2. **智能重试** - 失败自动重试，避免数据丢失
3. **内存管理** - 及时清理处理记录，避免内存泄漏
4. **异步处理** - 非阻塞式操作，不影响用户界面

## 🎉 总结

现在你的自动跟进系统具备了完整的POST提交功能：

✅ **完整的API客户端** - 基于真实抓包数据构建  
✅ **自动批量处理** - 智能化的跟进流程  
✅ **错误处理机制** - 稳定可靠的运行保障  
✅ **调试和监控** - 完善的日志和统计功能  
✅ **易于集成** - 与现有插件无缝结合  

你可以立即开始使用这些功能，或者根据具体需求进行进一步的定制和优化。
