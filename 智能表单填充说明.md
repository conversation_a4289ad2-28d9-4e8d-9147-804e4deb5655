# 智能表单填充功能说明

## 功能概述

新增的智能表单填充功能可以自动识别跟进对话框中的各种表单字段，并根据预设的配置自动填充合适的值，大大减少手工操作时间。

## 支持的字段类型

### 客户意愿部分
- **线索是否有效**: 自动选择"有效线索"
- **意向车系**: 优先选择"Q5L"，备选包括A4L、A6L等
- **意向车型**: 根据车系自动匹配对应车型
- **购车预算**: 随机选择合理的预算范围（25-50万）
- **计划购买方式**: 优先选择"按揭"
- **客户关注点**: 多选字段，自动选择"配置"等关注点
- **购置类型**: 默认选择"新购"
- **政策关注**: 多选字段，自动选择相关政策

### 跟进信息部分
- **线索等级**: 默认选择"A（7天内跟进）"
- **跟进状态**: 默认选择"再次待跟进"
- **跟进方式**: 默认选择"电话沟通"
- **下个跟进顾问**: 保持当前选择或随机选择
- **预购日期**: 自动设置为未来7-30天的随机日期
- **计划跟进时间**: 自动设置为未来1-3天的工作时间
- **跟进说明**: 自动填入预设的跟进说明模板

## 使用方法

1. **启用功能**: 在插件弹窗的"运行设置"区域，勾选"自动填充表单字段"选项
2. **配置消息**: 在"回复内容"中输入要发送的跟进消息
3. **开始运行**: 点击"开始运行"按钮
4. **自动处理**: 插件会自动：
   - 点击跟进按钮
   - 填写跟进消息
   - 智能填充所有表单字段
   - 保存并关闭对话框

## 智能特性

### 1. 自适应填充
- 检测字段是否已有值，避免覆盖现有数据
- 根据字段类型选择合适的填充策略
- 支持单选、多选、日期、文本等不同字段类型

### 2. 随机化处理
- 日期字段使用随机时间，避免数据过于规律
- 选择项随机选择，增加真实性
- 跟进说明使用多个模板随机选择

### 3. 错误处理
- 单个字段填充失败不影响其他字段
- 详细的日志记录，便于问题排查
- 自动跳过不支持的字段类型

## 配置选项

### 默认值配置
可以通过修改`FORM_FIELD_CONFIG`对象来调整默认值：

```javascript
'线索等级': {
  options: ['H（2天内跟进）', 'A（7天内跟进）', 'B（30天内跟进）'],
  defaultValue: 'A（7天内跟进）'  // 可修改默认选择
}
```

### 跟进说明模板
可以在`fillTextAreas`函数中添加更多跟进说明模板：

```javascript
const remarks = [
  '客户对车型表现出浓厚兴趣，计划近期到店详细了解',
  '已向客户介绍车型基本信息，客户反馈良好',
  // 添加更多模板...
];
```

## 注意事项

1. **首次使用**: 建议先在测试环境中验证功能正常
2. **数据检查**: 虽然有智能填充，建议定期检查填充的数据是否合理
3. **个性化调整**: 可根据实际业务需求调整默认值和选项
4. **禁用选项**: 如不需要自动填充，可取消勾选相应选项

## 技术实现

- 使用DOM查询和事件模拟技术
- 支持Element UI组件的自动操作
- 异步处理确保操作的稳定性
- 详细的日志记录便于调试

## 更新日志

- v1.2: 新增智能表单填充功能
- 支持所有主要表单字段的自动填充
- 添加用户配置选项
- 优化错误处理和日志记录
