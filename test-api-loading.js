/**
 * 测试API加载的脚本
 * 在浏览器控制台中运行此脚本来验证API是否正确加载
 */

console.log('🧪 开始测试API加载状态...');

// 检查API客户端类是否存在
function checkAPIClass() {
    console.log('\n📋 1. 检查API客户端类');
    
    if (typeof window.AutoFollowUpAPI === 'function') {
        console.log('✅ AutoFollowUpAPI类已定义');
        return true;
    } else {
        console.log('❌ AutoFollowUpAPI类未定义');
        console.log('💡 尝试手动加载API客户端...');
        return false;
    }
}

// 检查API客户端实例是否存在
function checkAPIInstance() {
    console.log('\n📋 2. 检查API客户端实例');
    
    if (window.apiClient && typeof window.apiClient === 'object') {
        console.log('✅ apiClient实例存在');
        console.log('📊 实例类型:', typeof window.apiClient);
        console.log('📊 实例构造函数:', window.apiClient.constructor.name);
        return true;
    } else {
        console.log('❌ apiClient实例不存在');
        return false;
    }
}

// 检查自动跟进处理器类是否存在
function checkProcessorClass() {
    console.log('\n📋 3. 检查自动跟进处理器类');
    
    if (typeof window.AutoFollowProcessor === 'function') {
        console.log('✅ AutoFollowProcessor类已定义');
        return true;
    } else {
        console.log('❌ AutoFollowProcessor类未定义');
        return false;
    }
}

// 检查自动跟进处理器实例是否存在
function checkProcessorInstance() {
    console.log('\n📋 4. 检查自动跟进处理器实例');
    
    if (window.autoFollowProcessor && typeof window.autoFollowProcessor === 'object') {
        console.log('✅ autoFollowProcessor实例存在');
        console.log('📊 实例类型:', typeof window.autoFollowProcessor);
        console.log('📊 实例构造函数:', window.autoFollowProcessor.constructor.name);
        return true;
    } else {
        console.log('❌ autoFollowProcessor实例不存在');
        return false;
    }
}

// 手动加载API客户端
async function manualLoadAPI() {
    console.log('\n🔧 尝试手动加载API客户端...');
    
    try {
        // 检查Chrome扩展API
        if (typeof chrome === 'undefined' || !chrome.runtime) {
            throw new Error('Chrome扩展API不可用');
        }
        
        // 加载API客户端
        const script1 = document.createElement('script');
        script1.src = chrome.runtime.getURL('api-client.js');
        
        await new Promise((resolve, reject) => {
            script1.onload = () => {
                console.log('✅ API客户端脚本加载成功');
                resolve();
            };
            script1.onerror = () => {
                reject(new Error('API客户端脚本加载失败'));
            };
            document.head.appendChild(script1);
        });
        
        // 等待实例创建
        await new Promise(resolve => setTimeout(resolve, 200));
        
        // 加载自动跟进处理器
        const script2 = document.createElement('script');
        script2.src = chrome.runtime.getURL('auto-follow-processor.js');
        
        await new Promise((resolve, reject) => {
            script2.onload = () => {
                console.log('✅ 自动跟进处理器脚本加载成功');
                resolve();
            };
            script2.onerror = () => {
                reject(new Error('自动跟进处理器脚本加载失败'));
            };
            document.head.appendChild(script2);
        });
        
        // 等待实例创建
        await new Promise(resolve => setTimeout(resolve, 200));
        
        console.log('✅ 手动加载完成');
        return true;
        
    } catch (error) {
        console.log('❌ 手动加载失败:', error.message);
        return false;
    }
}

// 测试API功能
async function testAPIFunctionality() {
    console.log('\n📋 5. 测试API功能');
    
    if (!window.apiClient) {
        console.log('❌ 无法测试，apiClient不存在');
        return false;
    }
    
    try {
        // 测试获取客户列表
        console.log('🔄 测试获取客户列表...');
        const result = await window.apiClient.getLeadList({ pageSize: 1 });
        
        if (result && result.code === '000000') {
            console.log('✅ API功能测试成功');
            console.log('📊 客户总数:', result.data?.totalCount || 0);
            return true;
        } else {
            console.log('⚠️ API返回异常:', result?.description || '未知错误');
            return false;
        }
    } catch (error) {
        console.log('❌ API功能测试失败:', error.message);
        return false;
    }
}

// 运行完整测试
async function runFullTest() {
    console.log('🚀 开始完整API加载测试');
    console.log('='.repeat(50));
    
    const results = {
        apiClass: checkAPIClass(),
        apiInstance: checkAPIInstance(),
        processorClass: checkProcessorClass(),
        processorInstance: checkProcessorInstance()
    };
    
    // 如果API没有加载，尝试手动加载
    if (!results.apiClass || !results.apiInstance || !results.processorClass || !results.processorInstance) {
        console.log('\n🔧 检测到API未完全加载，尝试手动加载...');
        const loadSuccess = await manualLoadAPI();
        
        if (loadSuccess) {
            // 重新检查
            results.apiClass = checkAPIClass();
            results.apiInstance = checkAPIInstance();
            results.processorClass = checkProcessorClass();
            results.processorInstance = checkProcessorInstance();
        }
    }
    
    // 测试API功能
    if (results.apiInstance) {
        results.apiFunctionality = await testAPIFunctionality();
    }
    
    // 输出总结
    console.log('\n📊 测试结果总结');
    console.log('='.repeat(50));
    
    const passCount = Object.values(results).filter(Boolean).length;
    const totalCount = Object.keys(results).length;
    
    console.log(`总测试项: ${totalCount}`);
    console.log(`通过: ${passCount}`);
    console.log(`失败: ${totalCount - passCount}`);
    
    Object.entries(results).forEach(([key, value]) => {
        const status = value ? '✅' : '❌';
        const name = key.replace(/([A-Z])/g, ' $1').toLowerCase();
        console.log(`${status} ${name}: ${value ? '正常' : '异常'}`);
    });
    
    if (passCount === totalCount) {
        console.log('\n🎉 所有测试通过！POST批量提交功能可以正常使用');
    } else {
        console.log('\n⚠️ 部分测试失败，请查看详细信息');
        
        // 提供修复建议
        console.log('\n💡 修复建议:');
        if (!results.apiClass || !results.apiInstance) {
            console.log('1. 重新加载插件 (chrome://extensions/)');
            console.log('2. 刷新页面后重试');
            console.log('3. 检查是否在正确的CRM域名下');
        }
        if (!results.processorClass || !results.processorInstance) {
            console.log('4. 确保API客户端先加载完成');
            console.log('5. 检查网络连接是否正常');
        }
        if (results.apiInstance && !results.apiFunctionality) {
            console.log('6. 检查是否已登录CRM系统');
            console.log('7. 检查网络连接和API权限');
        }
    }
    
    return results;
}

// 快速修复函数
async function quickFix() {
    console.log('🔧 执行快速修复...');
    
    try {
        // 1. 手动加载API
        await manualLoadAPI();
        
        // 2. 等待加载完成
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 3. 验证结果
        const apiOK = window.apiClient && typeof window.apiClient === 'object';
        const processorOK = window.autoFollowProcessor && typeof window.autoFollowProcessor === 'object';
        
        if (apiOK && processorOK) {
            console.log('✅ 快速修复成功！');
            
            // 通知插件
            if (window.Statistics) {
                window.Statistics.addLog('🚀 POST提交功能已就绪');
            }
            
            return true;
        } else {
            console.log('❌ 快速修复失败');
            return false;
        }
    } catch (error) {
        console.log('❌ 快速修复异常:', error.message);
        return false;
    }
}

// 导出函数
window.testAPILoading = {
    runFullTest,
    quickFix,
    manualLoadAPI,
    testAPIFunctionality,
    checkAPIClass,
    checkAPIInstance,
    checkProcessorClass,
    checkProcessorInstance
};

// 自动运行测试
console.log('🧪 API加载测试工具已准备就绪');
console.log('💡 使用方法:');
console.log('   - runFullTest() : 运行完整测试');
console.log('   - quickFix() : 执行快速修复');
console.log('   - window.testAPILoading : 访问所有测试函数');

// 延迟自动运行
setTimeout(() => {
    console.log('\n🔄 自动开始API加载测试...');
    runFullTest();
}, 1000);
