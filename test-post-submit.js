/**
 * POST提交功能测试脚本
 * 在浏览器控制台中运行此脚本来测试API功能
 */

// 测试配置
const TEST_CONFIG = {
    // 测试用的客户ID (从你的抓包数据中获取)
    testLeadId: 120392904,
    
    // 测试延迟 (毫秒)
    testDelay: 1000,
    
    // 是否启用详细日志
    verbose: true
};

/**
 * 测试日志输出
 */
function testLog(message, data = null, level = 'info') {
    const timestamp = new Date().toLocaleString();
    const prefix = level === 'error' ? '❌' : level === 'warn' ? '⚠️' : level === 'success' ? '✅' : 'ℹ️';
    
    console.log(`${prefix} [测试] ${timestamp} - ${message}`);
    if (data && TEST_CONFIG.verbose) {
        console.log(data);
    }
}

/**
 * 延迟函数
 */
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 测试1: 检查API客户端是否已加载
 */
async function test1_CheckAPIClient() {
    testLog('开始测试: 检查API客户端');
    
    try {
        if (typeof AutoFollowUpAPI === 'undefined') {
            throw new Error('AutoFollowUpAPI 类未定义');
        }
        
        if (!window.apiClient) {
            throw new Error('全局 apiClient 实例不存在');
        }
        
        testLog('API客户端检查通过', null, 'success');
        return true;
    } catch (error) {
        testLog(`API客户端检查失败: ${error.message}`, error, 'error');
        return false;
    }
}

/**
 * 测试2: 检查自动跟进处理器
 */
async function test2_CheckProcessor() {
    testLog('开始测试: 检查自动跟进处理器');
    
    try {
        if (typeof AutoFollowProcessor === 'undefined') {
            throw new Error('AutoFollowProcessor 类未定义');
        }
        
        if (!window.autoFollowProcessor) {
            throw new Error('全局 autoFollowProcessor 实例不存在');
        }
        
        testLog('自动跟进处理器检查通过', null, 'success');
        return true;
    } catch (error) {
        testLog(`自动跟进处理器检查失败: ${error.message}`, error, 'error');
        return false;
    }
}

/**
 * 测试3: 测试获取客户列表
 */
async function test3_GetLeadList() {
    testLog('开始测试: 获取客户列表');
    
    try {
        const params = {
            state: '201',
            pageSize: 5
        };
        
        const result = await window.apiClient.getLeadList(params);
        
        if (!result || result.code !== '000000') {
            throw new Error(`API返回错误: ${result?.description || '未知错误'}`);
        }
        
        const totalCount = result.data?.totalCount || 0;
        const pageData = result.data?.pageData || [];
        
        testLog(`获取客户列表成功: 总计${totalCount}条，当前页${pageData.length}条`, result, 'success');
        return result;
    } catch (error) {
        testLog(`获取客户列表失败: ${error.message}`, error, 'error');
        return null;
    }
}

/**
 * 测试4: 测试获取客户详情
 */
async function test4_GetLeadDetail() {
    testLog('开始测试: 获取客户详情');
    
    try {
        const result = await window.apiClient.getLeadDetail(TEST_CONFIG.testLeadId);
        
        if (!result || result.code !== '000000') {
            throw new Error(`API返回错误: ${result?.description || '未知错误'}`);
        }
        
        const leadData = result.data;
        testLog(`获取客户详情成功: ${leadData?.userName || '未知'} (${leadData?.userMobile || '无手机'})`, result, 'success');
        return result;
    } catch (error) {
        testLog(`获取客户详情失败: ${error.message}`, error, 'error');
        return null;
    }
}

/**
 * 测试5: 测试POST提交跟进记录
 */
async function test5_SubmitFollowUp() {
    testLog('开始测试: POST提交跟进记录');
    
    try {
        // 先获取客户详情
        const leadDetail = await window.apiClient.getLeadDetail(TEST_CONFIG.testLeadId);
        if (!leadDetail || !leadDetail.data) {
            throw new Error('无法获取客户详情');
        }
        
        const leadData = leadDetail.data;
        
        // 构建测试跟进数据
        const followUpData = {
            leadId: leadData.leadId,
            userName: leadData.userName,
            userMobile: leadData.userMobile,
            intentionSeries: `${leadData.intentionSeriesId}_${leadData.intentionSeriesName}`,
            intentionSeriesId: leadData.intentionSeriesId,
            intentionSeriesName: leadData.intentionSeriesName,
            consultant: leadData.consultant,
            consultantId: leadData.consultantId,
            remark: `测试POST提交 - ${new Date().toLocaleString()}`,
            nextFollowTime: window.apiClient.formatDateTime(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)),
            level: "2_B（30天内跟进）_720",
            levelId: "2",
            levelName: "B（30天内跟进）"
        };
        
        testLog('准备提交跟进数据', followUpData);
        
        const result = await window.apiClient.submitFollowUp(followUpData);
        
        if (!result || result.code !== '000000') {
            throw new Error(`POST提交失败: ${result?.description || '未知错误'}`);
        }
        
        testLog('POST提交跟进记录成功', result, 'success');
        return result;
    } catch (error) {
        testLog(`POST提交跟进记录失败: ${error.message}`, error, 'error');
        return null;
    }
}

/**
 * 测试6: 测试自动跟进处理器
 */
async function test6_AutoProcessor() {
    testLog('开始测试: 自动跟进处理器');
    
    try {
        // 配置测试参数
        const config = {
            batchSize: 2,
            delayBetweenRequests: 1000,
            maxRetries: 2,
            autoRemark: true
        };
        
        window.autoFollowProcessor.setConfig(config);
        testLog('自动跟进处理器配置完成', config);
        
        // 注意: 这里只是测试配置，不实际运行自动跟进
        // 因为实际运行可能会影响真实数据
        testLog('自动跟进处理器测试完成 (仅配置测试)', null, 'success');
        return true;
    } catch (error) {
        testLog(`自动跟进处理器测试失败: ${error.message}`, error, 'error');
        return false;
    }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
    testLog('🚀 开始运行POST提交功能完整测试套件');
    
    const tests = [
        { name: '检查API客户端', func: test1_CheckAPIClient },
        { name: '检查自动跟进处理器', func: test2_CheckProcessor },
        { name: '获取客户列表', func: test3_GetLeadList },
        { name: '获取客户详情', func: test4_GetLeadDetail },
        { name: 'POST提交跟进记录', func: test5_SubmitFollowUp },
        { name: '自动跟进处理器', func: test6_AutoProcessor }
    ];
    
    const results = [];
    
    for (let i = 0; i < tests.length; i++) {
        const test = tests[i];
        testLog(`\n📋 执行测试 ${i + 1}/${tests.length}: ${test.name}`);
        
        try {
            const result = await test.func();
            results.push({
                name: test.name,
                success: !!result,
                result: result
            });
            
            if (result) {
                testLog(`✅ 测试通过: ${test.name}`, null, 'success');
            } else {
                testLog(`❌ 测试失败: ${test.name}`, null, 'error');
            }
        } catch (error) {
            testLog(`❌ 测试异常: ${test.name} - ${error.message}`, error, 'error');
            results.push({
                name: test.name,
                success: false,
                error: error.message
            });
        }
        
        // 测试间隔
        if (i < tests.length - 1) {
            await delay(TEST_CONFIG.testDelay);
        }
    }
    
    // 输出测试总结
    testLog('\n📊 测试总结报告');
    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;
    
    testLog(`总测试数: ${totalCount}`);
    testLog(`成功: ${successCount}`);
    testLog(`失败: ${totalCount - successCount}`);
    testLog(`成功率: ${((successCount / totalCount) * 100).toFixed(1)}%`);
    
    // 详细结果
    results.forEach((result, index) => {
        const status = result.success ? '✅' : '❌';
        testLog(`${status} ${index + 1}. ${result.name}`);
        if (result.error) {
            testLog(`   错误: ${result.error}`);
        }
    });
    
    if (successCount === totalCount) {
        testLog('\n🎉 所有测试通过！POST提交功能工作正常', null, 'success');
    } else {
        testLog('\n⚠️ 部分测试失败，请检查相关功能', null, 'warn');
    }
    
    return results;
}

/**
 * 快速测试函数 (仅测试基本功能)
 */
async function quickTest() {
    testLog('🏃‍♂️ 开始快速测试');
    
    try {
        // 检查基本组件
        if (!window.apiClient || !window.autoFollowProcessor) {
            throw new Error('API组件未正确加载');
        }
        
        // 测试获取客户列表
        const leadList = await window.apiClient.getLeadList({ pageSize: 1 });
        if (!leadList || leadList.code !== '000000') {
            throw new Error('获取客户列表失败');
        }
        
        testLog('✅ 快速测试通过！基本功能正常', null, 'success');
        return true;
    } catch (error) {
        testLog(`❌ 快速测试失败: ${error.message}`, error, 'error');
        return false;
    }
}

// 导出测试函数到全局
window.testPostSubmit = {
    runAllTests,
    quickTest,
    test1_CheckAPIClient,
    test2_CheckProcessor,
    test3_GetLeadList,
    test4_GetLeadDetail,
    test5_SubmitFollowUp,
    test6_AutoProcessor
};

// 自动运行快速测试 (如果在支持的环境中)
if (typeof window !== 'undefined' && window.location) {
    console.log('🧪 POST提交功能测试脚本已加载');
    console.log('💡 使用方法:');
    console.log('   - runAllTests() : 运行完整测试套件');
    console.log('   - quickTest() : 运行快速测试');
    console.log('   - window.testPostSubmit : 访问所有测试函数');
}
