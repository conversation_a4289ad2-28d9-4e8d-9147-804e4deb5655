<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POST提交功能演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h3 {
            color: #007bff;
            border-left: 4px solid #007bff;
            padding-left: 10px;
        }
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 15px 0;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-info {
            background-color: #17a2b8;
            color: white;
        }
        button:hover {
            opacity: 0.8;
            transform: translateY(-1px);
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        .log-container {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 5px;
            border-radius: 3px;
        }
        .log-info { background-color: #d1ecf1; color: #0c5460; }
        .log-success { background-color: #d4edda; color: #155724; }
        .log-warning { background-color: #fff3cd; color: #856404; }
        .log-error { background-color: #f8d7da; color: #721c24; }
        .config-panel {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
        }
        .config-item {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .config-item label {
            min-width: 120px;
            font-weight: bold;
        }
        .config-item input, .config-item select {
            flex: 1;
            padding: 5px 10px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        .stats-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 POST提交功能演示</h1>
            <p>基于抓包数据分析的自动跟进API客户端</p>
        </div>

        <!-- 快速操作区 -->
        <div class="section">
            <h3>🎯 快速操作</h3>
            <div class="button-group">
                <button class="btn-primary" onclick="initializeAPI()">初始化API</button>
                <button class="btn-success" onclick="testGetLeadList()">获取客户列表</button>
                <button class="btn-info" onclick="testGetLeadDetail()">获取客户详情</button>
                <button class="btn-warning" onclick="testSubmitFollowUp()">测试跟进提交</button>
                <button class="btn-primary" onclick="startAutoFollow()">开始自动跟进</button>
                <button class="btn-danger" onclick="stopAutoFollow()">停止自动跟进</button>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="section">
            <h3>📊 运行统计</h3>
            <div class="stats-panel" id="statsPanel">
                <div class="stat-card">
                    <div class="stat-number" id="processedCount">0</div>
                    <div class="stat-label">已处理客户</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="successCount">0</div>
                    <div class="stat-label">成功提交</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="errorCount">0</div>
                    <div class="stat-label">失败次数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="runningStatus">停止</div>
                    <div class="stat-label">运行状态</div>
                </div>
            </div>
        </div>

        <!-- 配置面板 -->
        <div class="section">
            <h3>⚙️ 配置设置</h3>
            <div class="config-panel">
                <div class="config-item">
                    <label>批处理大小:</label>
                    <input type="number" id="batchSize" value="5" min="1" max="20">
                </div>
                <div class="config-item">
                    <label>请求间隔(秒):</label>
                    <input type="number" id="delayBetweenRequests" value="2" min="1" max="10">
                </div>
                <div class="config-item">
                    <label>最大重试次数:</label>
                    <input type="number" id="maxRetries" value="3" min="1" max="5">
                </div>
                <div class="config-item">
                    <label>自动生成备注:</label>
                    <select id="autoRemark">
                        <option value="true">启用</option>
                        <option value="false">禁用</option>
                    </select>
                </div>
                <div class="button-group">
                    <button class="btn-info" onclick="updateConfig()">更新配置</button>
                    <button class="btn-warning" onclick="resetConfig()">重置配置</button>
                </div>
            </div>
        </div>

        <!-- 日志输出 -->
        <div class="section">
            <h3>📝 运行日志</h3>
            <div class="button-group">
                <button class="btn-info" onclick="clearLogs()">清空日志</button>
                <button class="btn-success" onclick="exportLogs()">导出日志</button>
            </div>
            <div class="log-container" id="logContainer">
                <div class="log-entry log-info">等待操作...</div>
            </div>
        </div>

        <!-- API使用示例 -->
        <div class="section">
            <h3>💡 API使用示例</h3>
            <div class="code-block">
// 1. 初始化API客户端
const apiClient = new AutoFollowUpAPI();

// 2. 获取客户列表
const leadList = await apiClient.getLeadList({
    state: '201',  // 再次待跟进
    pageSize: 10
});

// 3. 获取客户详情
const leadDetail = await apiClient.getLeadDetail(120392904);

// 4. 提交跟进记录
const followUpData = {
    leadId: 120392904,
    userName: "西城李先生",
    userMobile: "13285462009",
    remark: "客户咨询车型信息，已发送详细资料"
};
const result = await apiClient.submitFollowUp(followUpData);

// 5. 使用自动跟进处理器
const processor = new AutoFollowProcessor();
await processor.startAutoFollow({
    batchSize: 5,
    delayBetweenRequests: 2000
});
            </div>
        </div>
    </div>

    <!-- 加载API文件 -->
    <script src="api-client.js"></script>
    <script src="auto-follow-processor.js"></script>
    
    <script>
        let logs = [];
        let stats = {
            processedCount: 0,
            successCount: 0,
            errorCount: 0,
            runningStatus: '停止'
        };

        // 添加日志
        function addLog(message, level = 'info') {
            const timestamp = new Date().toLocaleString();
            const logEntry = {
                timestamp,
                message,
                level
            };
            logs.push(logEntry);
            
            const logContainer = document.getElementById('logContainer');
            const logDiv = document.createElement('div');
            logDiv.className = `log-entry log-${level}`;
            logDiv.textContent = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logDiv);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 更新统计信息
        function updateStats() {
            document.getElementById('processedCount').textContent = stats.processedCount;
            document.getElementById('successCount').textContent = stats.successCount;
            document.getElementById('errorCount').textContent = stats.errorCount;
            document.getElementById('runningStatus').textContent = stats.runningStatus;
        }

        // 初始化API
        function initializeAPI() {
            try {
                if (window.apiClient) {
                    addLog('✅ API客户端已初始化', 'success');
                } else {
                    addLog('❌ API客户端初始化失败', 'error');
                    stats.errorCount++;
                }
                updateStats();
            } catch (error) {
                addLog(`❌ 初始化错误: ${error.message}`, 'error');
                stats.errorCount++;
                updateStats();
            }
        }

        // 测试获取客户列表
        async function testGetLeadList() {
            try {
                addLog('🔄 正在获取客户列表...', 'info');
                const result = await window.apiClient.getLeadList();
                addLog(`✅ 获取客户列表成功，共${result.data?.totalCount || 0}条记录`, 'success');
                stats.successCount++;
                updateStats();
            } catch (error) {
                addLog(`❌ 获取客户列表失败: ${error.message}`, 'error');
                stats.errorCount++;
                updateStats();
            }
        }

        // 测试获取客户详情
        async function testGetLeadDetail() {
            try {
                const leadId = prompt('请输入客户ID (例如: 120392904):');
                if (!leadId) return;
                
                addLog(`🔄 正在获取客户详情 (ID: ${leadId})...`, 'info');
                const result = await window.apiClient.getLeadDetail(leadId);
                addLog(`✅ 获取客户详情成功: ${result.data?.userName || '未知'}`, 'success');
                stats.successCount++;
                updateStats();
            } catch (error) {
                addLog(`❌ 获取客户详情失败: ${error.message}`, 'error');
                stats.errorCount++;
                updateStats();
            }
        }

        // 测试提交跟进
        async function testSubmitFollowUp() {
            try {
                const testData = {
                    leadId: 120392904,
                    userName: "测试客户",
                    userMobile: "13800000000",
                    remark: "测试跟进记录 - " + new Date().toLocaleString()
                };
                
                addLog('🔄 正在提交测试跟进记录...', 'info');
                const result = await window.apiClient.submitFollowUp(testData);
                
                if (result.code === '000000') {
                    addLog('✅ 跟进记录提交成功', 'success');
                    stats.successCount++;
                } else {
                    addLog(`⚠️ 跟进记录提交失败: ${result.description}`, 'warning');
                    stats.errorCount++;
                }
                updateStats();
            } catch (error) {
                addLog(`❌ 提交跟进记录失败: ${error.message}`, 'error');
                stats.errorCount++;
                updateStats();
            }
        }

        // 开始自动跟进
        async function startAutoFollow() {
            try {
                const config = getConfig();
                addLog('🚀 开始自动跟进流程...', 'info');
                stats.runningStatus = '运行中';
                updateStats();
                
                await window.autoFollowProcessor.startAutoFollow(config);
                
                stats.runningStatus = '已完成';
                updateStats();
            } catch (error) {
                addLog(`❌ 自动跟进失败: ${error.message}`, 'error');
                stats.errorCount++;
                stats.runningStatus = '错误';
                updateStats();
            }
        }

        // 停止自动跟进
        function stopAutoFollow() {
            window.autoFollowProcessor.stop();
            stats.runningStatus = '已停止';
            addLog('🛑 自动跟进已停止', 'warning');
            updateStats();
        }

        // 获取配置
        function getConfig() {
            return {
                batchSize: parseInt(document.getElementById('batchSize').value),
                delayBetweenRequests: parseInt(document.getElementById('delayBetweenRequests').value) * 1000,
                maxRetries: parseInt(document.getElementById('maxRetries').value),
                autoRemark: document.getElementById('autoRemark').value === 'true'
            };
        }

        // 更新配置
        function updateConfig() {
            const config = getConfig();
            window.autoFollowProcessor.setConfig(config);
            addLog('⚙️ 配置已更新', 'info');
        }

        // 重置配置
        function resetConfig() {
            document.getElementById('batchSize').value = 5;
            document.getElementById('delayBetweenRequests').value = 2;
            document.getElementById('maxRetries').value = 3;
            document.getElementById('autoRemark').value = 'true';
            addLog('🔄 配置已重置', 'info');
        }

        // 清空日志
        function clearLogs() {
            logs = [];
            document.getElementById('logContainer').innerHTML = '<div class="log-entry log-info">日志已清空</div>';
        }

        // 导出日志
        function exportLogs() {
            const logText = logs.map(log => `[${log.timestamp}] [${log.level.toUpperCase()}] ${log.message}`).join('\n');
            const blob = new Blob([logText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `auto-follow-logs-${new Date().toISOString().slice(0, 10)}.txt`;
            a.click();
            URL.revokeObjectURL(url);
            addLog('📄 日志已导出', 'success');
        }

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            addLog('🎉 POST提交功能演示页面已加载', 'success');
            updateStats();
        });
    </script>
</body>
</html>
