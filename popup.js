let isRunning = false;
let stats = {
  current: {
    total: 0,
    success: 0,
    fail: 0
  },
  total: {
    total: 0,
    success: 0,
    fail: 0
  }
};

// 加载保存的统计数据
function loadStats() {
  chrome.storage.local.get(['totalStats'], (data) => {
    if (data.totalStats) {
      stats.total = data.totalStats;
      updateStatsDisplay();
    }
  });
}

// 保存统计数据
function saveStats() {
  chrome.storage.local.set({
    totalStats: stats.total
  });
}

// 更新统计显示
function updateStatsDisplay() {
  // 更新当前统计
  document.getElementById('currentCount').textContent = stats.current.total;
  document.getElementById('currentSuccess').textContent = stats.current.success;
  document.getElementById('currentFail').textContent = stats.current.fail;
  
  // 更新总计统计
  document.getElementById('totalCount').textContent = stats.total.total;
  document.getElementById('totalSuccess').textContent = stats.total.success;
  document.getElementById('totalFail').textContent = stats.total.fail;
  
  // 计算并更新成功率
  const successRate = stats.total.total > 0 
    ? ((stats.total.success / stats.total.total) * 100).toFixed(1) 
    : 0;
  document.getElementById('successRate').textContent = `${successRate}%`;
}

// 更新运行状态
function updateStatus() {
  const statusElement = document.getElementById('status');
  statusElement.textContent = isRunning ? '运行中' : '已暂停';
  statusElement.className = isRunning ? 'status-running' : 'status-stopped';
}

// 优化日志处理函数
function addLog(message, type = 'info') {
  const logArea = document.querySelector("#logArea");
  if (!logArea) {
    console.error('找不到日志区域元素');
    return;
  }

  const timestamp = new Date().toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
  
  const logMessage = `[${timestamp}] ${message}`;
  const logEntry = {
    time: timestamp,
    message: message,
    type: type,
    id: Date.now() // 添加唯一标识
  };

  // 使用Set进行内存中的日志去重
  if (!window.logCache) {
    window.logCache = new Set();
  }

  const cacheKey = `${timestamp}-${message}`;
  if (!window.logCache.has(cacheKey)) {
    window.logCache.add(cacheKey);
    
    // 更新显示
    logArea.value = `${logMessage}\n${logArea.value}`;
    logArea.scrollTop = 0;

    // 异步保存到storage
    saveLogToStorage(logEntry);
  }
}

// 优化日志存储函数
async function saveLogToStorage(logEntry) {
  try {
    const data = await chrome.storage.local.get(['savedLogs']);
    const logs = data.savedLogs || [];
    
    // 检查最近的日志是否重复
    const isDuplicate = logs.length > 0 && 
      logs[0].message === logEntry.message &&
      Math.abs(new Date(logs[0].time) - new Date(logEntry.time)) < 2000;
    
    if (!isDuplicate) {
      logs.unshift(logEntry);
      
      // 限制日志数量，保留最新的100条
      if (logs.length > 100) {
        logs.length = 100;
      }

      await chrome.storage.local.set({ savedLogs: logs });
    }
  } catch (error) {
    console.error('保存日志失败:', error);
  }
}

// 优化日志加载函数
async function loadSavedLogs() {
  const logArea = document.querySelector("#logArea");
  if (!logArea) {
    console.error('找不到日志区域元素');
    return;
  }

  try {
    const data = await chrome.storage.local.get(['savedLogs']);
    if (!data.savedLogs?.length) return;

    // 初始化日志缓存
    window.logCache = new Set();
    
    // 使用Map进行去重，保持时间顺序
    const uniqueLogs = new Map();
    data.savedLogs.forEach(log => {
      const key = `${log.time}-${log.message}`;
      if (!uniqueLogs.has(key)) {
        uniqueLogs.set(key, log);
        window.logCache.add(key);
      }
    });

    // 构建日志文本
    const logText = Array.from(uniqueLogs.values())
      .sort((a, b) => new Date(b.time) - new Date(a.time))
      .map(log => `[${log.time}] ${log.message}`)
      .join('\n');

    logArea.value = logText;
    logArea.scrollTop = 0;
  } catch (error) {
    console.error('加载日志失败:', error);
  }
}

// 优化清空日志函数
async function clearLogs() {
  const logArea = document.querySelector("#logArea");
  if (!logArea) return;

  try {
    // 清空显示
    logArea.value = '';
    
    // 清空缓存
    window.logCache = new Set();
    
    // 清空存储
    await chrome.storage.local.set({ savedLogs: [] });
    
    // 延迟添加清空提示
    setTimeout(() => {
      addLog('已清空日志');
    }, 100);
  } catch (error) {
    console.error('清空日志失败:', error);
  }
}

// 修改清空按钮事件处理
document.getElementById('clearLogBtn').addEventListener('click', clearLogs);

// 添加状态初始化函数
async function initializeState() {
  try {
    const tabs = await chrome.tabs.query({
      active: true,
      currentWindow: true
    });
    
    if (!tabs || !tabs[0]) return;

    // 获取当前状态
    chrome.tabs.sendMessage(tabs[0].id, { action: 'getState' }, response => {
      if (chrome.runtime.lastError) {
        console.log('获取状态失败:', chrome.runtime.lastError);
        return;
      }

      if (response) {
        isRunning = response.isRunning;
        updateButtonState(isRunning);
        
        if (response.stats) {
          updateUI(response.stats);
        }
      }
    });
  } catch (error) {
    console.error('初始化状态失败:', error);
  }
}

// 修改 DOMContentLoaded 事件处理
document.addEventListener('DOMContentLoaded', () => {
  loadSavedLogs();
  initializeState(); // 添加状态初始化
});

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('收到消息:', request);

  if (request.action === 'updateStats') {
    updateStats(request.stats);
  }
  
  // 处理休眠状态更新
  if (request.action === 'updateSleepStatus') {
    const statusText = document.getElementById('status');
    if (!statusText) return;

    const { isSleeping, timeDisplay, remainingMs, isRunning: runningState } = request.data;
    
    if (isSleeping && remainingMs > 0) {
      statusText.textContent = `休眠中 (${timeDisplay})`;
      statusText.className = 'status-badge sleeping';
      isRunning = runningState;
    } else if (!isSleeping && runningState) {
      statusText.textContent = '运行中';
      statusText.className = 'status-badge running';
      isRunning = runningState;
    } else {
      statusText.textContent = '已停止';
      statusText.className = 'status-badge stopped';
      isRunning = false;
    }
    
    updateButtonState(isRunning);
  }
  
  if (request.action === 'log') {
    console.log('收到日志:', request.data);
    const { timestamp, message, type } = request.data;
    addLog(message, type);
  }
});

// 处理操作结果
function handleOperationResult(success, message) {
  stats.current.total++;
  if (success) {
    stats.current.success++;
    stats.total.success++;
    addLog(message || '操作成功', 'success');
  } else {
    stats.current.fail++;
    stats.total.fail++;
    addLog(message || '操作失败', 'error');
  }
  stats.total.total++;
  
  updateStatsDisplay();
  saveStats();
}

// 重置当前统计
function resetCurrentStats() {
  stats.current = {
    total: 0,
    success: 0,
    fail: 0
  };
  updateStatsDisplay();
}

// 发送消息到content script
function sendMessageToContentScript(message) {
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    if (tabs[0]) {
      console.log('发送消息到content script:', message);
      chrome.tabs.sendMessage(tabs[0].id, message, function(response) {
        console.log('收到content script响应:', response);
      });
    } else {
      console.error('未找到活动标签页');
    }
  });
}

// 修改按钮状态处理
function updateButtonState(isRunning) {
  const startBtn = document.getElementById('startBtn');
  const stopBtn = document.getElementById('stopBtn');
  
  if (startBtn && stopBtn) {
    startBtn.disabled = isRunning;
    stopBtn.disabled = !isRunning;
  }
}

// 修改开始按钮事件处理
document.getElementById('startBtn').addEventListener('click', async () => {
  try {
    const tabs = await chrome.tabs.query({
      active: true,
      currentWindow: true
    });
    
    if (!tabs || !tabs[0]) {
      addLog('未找到活动标签页', 'error');
      return;
    }

    // 先尝试注入 content script
    try {
      await chrome.scripting.executeScript({
        target: { tabId: tabs[0].id },
        files: ['content.js']
      });
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      if (!error.message.includes('already exists')) {
        addLog('脚本注入失败: ' + error.message, 'error');
        return;
      }
    }

    // 发送启动命令
    chrome.tabs.sendMessage(tabs[0].id, {
      action: 'start',
      settings: getSettings()
    }, response => {
      if (chrome.runtime.lastError) {
        addLog('启动失败: ' + chrome.runtime.lastError.message, 'error');
        return;
      }

      if (response && response.status === 'started') {
        isRunning = true;
        // 立即更新UI状态
        const statusText = document.getElementById('status');
        if (statusText) {
          statusText.textContent = '运行中';
          statusText.className = 'status-badge running';
        }
        updateButtonState(true);
        addLog('开始运行自动跟进');
      } else {
        addLog('启动失败: ' + (response?.error || '未知错误'), 'error');
      }
    });

  } catch (error) {
    console.error('启动失败:', error);
    addLog('启动失败: ' + error.message, 'error');
  }
});

// 修改停止按钮事件处理
document.getElementById('stopBtn').addEventListener('click', async () => {
  try {
    const tabs = await chrome.tabs.query({
      active: true,
      currentWindow: true
    });
    
    if (tabs && tabs[0]) {
      chrome.tabs.sendMessage(tabs[0].id, { action: 'stop' }, response => {
        isRunning = false;
        
        // 立即更新UI状态
        const statusText = document.getElementById('status');
        if (statusText) {
          statusText.textContent = '已停止';
          statusText.className = 'status-badge stopped';
        }
        
        updateButtonState(false);
        addLog('停止运行自动跟进');

        // 清除休眠状态
        chrome.runtime.sendMessage({
          action: 'updateSleepStatus',
          data: {
            isSleeping: false,
            timeDisplay: '',
            remainingMs: 0,
            isRunning: false
          }
        });
      });
    }
  } catch (error) {
    console.error('停止失败:', error);
    addLog('停止失败: ' + error.message, 'error');
  }
});

function getSettings() {
  return {
    minWaitTime: parseFloat(document.getElementById('minWaitTime').value),
    maxWaitTime: parseFloat(document.getElementById('maxWaitTime').value),
    messages: document.getElementById('messages').value.split('\n').filter(msg => msg.trim()),
    autoFillForm: document.getElementById('autoFillForm').checked,
    forceRequired: document.getElementById('forceRequired').checked
  };
}

function saveSettings() {
  chrome.storage.local.set(getSettings());
}

function loadSettings() {
  chrome.storage.local.get([
    'minWaitTime', 'maxWaitTime', 'messages', 'autoFillForm', 'forceRequired',
    'enableConcurrent', 'maxConcurrency', 'batchSize'
  ], (data) => {
    // 等待时间设置（新默认值：0.01-0.1秒）
    if (data.minWaitTime !== undefined) {
      document.getElementById('minWaitTime').value = data.minWaitTime;
    } else {
      document.getElementById('minWaitTime').value = 0.01; // 新默认值
    }

    if (data.maxWaitTime !== undefined) {
      document.getElementById('maxWaitTime').value = data.maxWaitTime;
    } else {
      document.getElementById('maxWaitTime').value = 0.1; // 新默认值
    }

    if (data.messages) {
      document.getElementById('messages').value = data.messages.join('\n');
    }

    if (data.autoFillForm !== undefined) {
      document.getElementById('autoFillForm').checked = data.autoFillForm;
    }

    // 强制填充必填字段（新默认值：不勾选）
    if (data.forceRequired !== undefined) {
      document.getElementById('forceRequired').checked = data.forceRequired;
    } else {
      document.getElementById('forceRequired').checked = false; // 新默认值
    }

    // 并发设置
    if (data.enableConcurrent !== undefined) {
      document.getElementById('enableConcurrent').checked = data.enableConcurrent;
    }
    if (data.maxConcurrency) {
      document.getElementById('maxConcurrency').value = data.maxConcurrency;
    }
    if (data.batchSize) {
      document.getElementById('batchSize').value = data.batchSize;
    }
  });
}

// 添加输入验证
document.getElementById('minWaitTime').addEventListener('change', function() {
  const min = parseFloat(this.value);
  const max = parseFloat(document.getElementById('maxWaitTime').value);
  if (min > max) {
    document.getElementById('maxWaitTime').value = min;
  }
  saveSettings();
});

document.getElementById('maxWaitTime').addEventListener('change', function() {
  const max = parseFloat(this.value);
  const min = parseFloat(document.getElementById('minWaitTime').value);
  if (max < min) {
    document.getElementById('minWaitTime').value = max;
  }
  saveSettings();
});

// 自动保存文本内容
document.getElementById('messages').addEventListener('input', saveSettings);

// 自动保存复选框状态
document.getElementById('autoFillForm').addEventListener('change', saveSettings);
document.getElementById('forceRequired').addEventListener('change', saveSettings);

// 初始化
loadSettings();
loadStats();

// 简化复位按钮事件监听
document.getElementById('resetStats').addEventListener('click', () => {
  chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
    chrome.tabs.sendMessage(tabs[0].id, {
      action: 'resetStats'
    }, (response) => {
      if (response && response.success) {
        // 直接更新显示
        updateStats({
          currentSuccess: 0,
          currentFail: 0,
          totalSuccess: 0,
          historyTotal: 0
        });
      }
    });
  });
});

// 修改更新统计信息函数
function updateStats(stats) {
  // 更新当前统计
  document.getElementById('currentSuccess').textContent = stats.successCount || 0;
  document.getElementById('currentFail').textContent = stats.failureCount || 0;
  
  // 更新历史统计
  document.getElementById('totalSuccess').textContent = stats.totalSuccess || 0;
  document.getElementById('historyTotal').textContent = stats.historyTotal || 0;

  // 计算总数和成功率
  const totalCount = (stats.successCount || 0) + (stats.failureCount || 0);
  document.getElementById('totalCount').textContent = totalCount;

  // 计算成功率
  const successRate = totalCount > 0 
    ? ((stats.successCount / totalCount) * 100).toFixed(1) 
    : '0.0';
  document.getElementById('successRate').textContent = `${successRate}%`;

  // 保存历史数据
  chrome.storage.local.set({
    historyStats: {
      totalSuccess: stats.totalSuccess || 0,
      historyTotal: stats.historyTotal || 0
    }
  });
}

// 在初始化时加载历史数据
function loadHistoryStats() {
  chrome.storage.local.get(['historyStats'], (data) => {
    if (data.historyStats) {
      updateStats({
        successCount: 0,
        failureCount: 0,
        totalSuccess: data.historyStats.totalSuccess,
        historyTotal: data.historyStats.historyTotal
      });
    }
  });
}

// 初始化时调用
loadHistoryStats();

// 修改更新UI函数
function updateUI(stats) {
  const statusText = document.getElementById('status');
  if (!statusText) return;

  if (!stats || !stats.status) return;

  const { isSleeping, sleepEndTime } = stats.status;
  
  if (isSleeping && sleepEndTime) {
    const remainingMs = new Date(sleepEndTime) - new Date();
    if (remainingMs > 0) {
      const remainingMinutes = Math.floor(remainingMs / 60000);
      const remainingSeconds = Math.floor((remainingMs % 60000) / 1000);
      const timeDisplay = `${remainingMinutes}:${remainingSeconds.toString().padStart(2, '0')}`;
      
      statusText.textContent = `休眠中 (${timeDisplay})`;
      statusText.className = 'status-badge sleeping';
      isRunning = true;
    } else {
      statusText.textContent = '运行中';
      statusText.className = 'status-badge running';
      isRunning = true;
    }
  } else if (stats.status.isRunning) {
    statusText.textContent = '运行中';
    statusText.className = 'status-badge running';
    isRunning = true;
  } else {
    statusText.textContent = '已停止';
    statusText.className = 'status-badge stopped';
    isRunning = false;
  }

  updateButtonState(isRunning);
  updateStats(stats);
}

// POST直接提交功能按钮事件处理

// 测试数据提取
document.getElementById('testDataBtn').addEventListener('click', () => {
  chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
    chrome.tabs.sendMessage(tabs[0].id, {
      action: 'testDataExtraction'
    }, (response) => {
      if (response && response.success) {
        const customers = response.customers;
        alert(`📊 数据提取测试完成！

提取到 ${customers.length} 条客户数据

${customers.length > 0 ?
  `前3条数据示例:\n${customers.slice(0, 3).map((c, i) =>
    `${i+1}. ID: ${c.leadId}\n   姓名: ${c.userName}\n   手机: ${c.userMobile}`
  ).join('\n\n')}

✅ 数据提取成功！可以使用POST模式。` :
  `❌ 未提取到数据

可能原因：
1. 页面未完全加载
2. 客户列表为空
3. 页面结构发生变化

建议刷新页面后重试。`}`);
      } else {
        alert(`❌ 数据提取失败

错误: ${response?.error || '未知错误'}

请确保页面已加载客户列表。`);
      }
    });
  });
});

// 测试POST提交
document.getElementById('testPostBtn').addEventListener('click', () => {
  chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
    chrome.tabs.sendMessage(tabs[0].id, {
      action: 'testPostSubmit'
    }, (response) => {
      if (response && response.success) {
        const result = response.result;
        alert(`🧪 POST测试完成！

结果: ${result.success ? '✅ 成功' : '❌ 失败'}
客户: ${result.customer.userName}
线索ID: ${result.customer.leadId}

${result.success ? '测试成功！可以使用POST模式进行批量处理。' : `失败原因: ${result.error}`}`);
      } else {
        alert(`❌ 测试失败

错误: ${response?.error || '未知错误'}

请确保：
1. 页面已加载客户列表
2. 网络连接正常
3. 登录状态有效`);
      }
    });
  });
});

// POST批量模式
document.getElementById('postModeBtn').addEventListener('click', () => {
  const confirmed = confirm(`🚀 POST直接提交模式

这将绕过UI操作，直接调用API进行批量跟进：

⚡ 优势：
• 速度提升10-60倍
• 无需等待页面操作
• 基于真实抓包数据

⚠️ 注意：
• 请确保页面已加载客户列表
• 确保网络连接稳定
• 确保登录状态有效

确定要启动POST模式吗？`);

  if (!confirmed) return;

  chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
    chrome.tabs.sendMessage(tabs[0].id, {
      action: 'startPostMode'
    }, (response) => {
      if (response && response.success) {
        const result = response.result;
        alert(`🎉 POST模式完成！

总记录数: ${result.total}
成功: ${result.successful}
失败: ${result.failed}
成功率: ${((result.successful / result.total) * 100).toFixed(1)}%

${result.successful > 0 ? '✅ 批量跟进成功完成！' : '❌ 所有记录都失败了，请检查网络和登录状态。'}`);
      } else {
        alert(`❌ POST模式失败

错误: ${response?.error || '未知错误'}

请确保：
1. 页面已加载客户列表
2. 网络连接正常
3. 登录状态有效
4. 没有其他程序在运行`);
      }
    });
  });
});