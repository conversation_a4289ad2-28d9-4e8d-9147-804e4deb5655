// 网络请求调试工具 - 专门用于诊断网络请求问题

class NetworkDebugger {
  constructor() {
    this.allRequests = [];
    this.isActive = false;
    this.originalMethods = {};
  }

  start() {
    if (this.isActive) return;
    
    this.isActive = true;
    this.allRequests = [];
    
    console.log('🔍 网络调试器启动');
    
    this.interceptAll();
    this.monitorPage();
  }

  stop() {
    this.isActive = false;
    this.restoreOriginalMethods();
    
    console.log('📊 网络调试器停止');
    console.log(`总共拦截 ${this.allRequests.length} 个请求`);
    
    this.analyzeResults();
  }

  interceptAll() {
    // 1. 拦截fetch
    if (window.fetch) {
      this.originalMethods.fetch = window.fetch;
      const debugger = this;
      
      window.fetch = function(...args) {
        if (debugger.isActive) {
          const url = args[0];
          const options = args[1] || {};
          
          console.log(`🌐 FETCH: ${options.method || 'GET'} ${url}`);
          
          debugger.allRequests.push({
            type: 'fetch',
            method: options.method || 'GET',
            url: url,
            data: options.body,
            timestamp: new Date()
          });
        }
        
        return debugger.originalMethods.fetch.apply(this, args);
      };
    }

    // 2. 拦截XMLHttpRequest
    if (window.XMLHttpRequest) {
      this.originalMethods.XMLHttpRequest = window.XMLHttpRequest;
      const debugger = this;
      
      window.XMLHttpRequest = function() {
        const xhr = new debugger.originalMethods.XMLHttpRequest();
        
        if (debugger.isActive) {
          const originalOpen = xhr.open;
          const originalSend = xhr.send;
          
          xhr.open = function(method, url) {
            this._debugMethod = method;
            this._debugUrl = url;
            
            console.log(`🌐 XHR OPEN: ${method} ${url}`);
            
            return originalOpen.apply(this, arguments);
          };
          
          xhr.send = function(data) {
            console.log(`🌐 XHR SEND: ${this._debugMethod} ${this._debugUrl}`);
            if (data) {
              console.log(`📦 XHR DATA:`, data);
            }
            
            debugger.allRequests.push({
              type: 'xhr',
              method: this._debugMethod,
              url: this._debugUrl,
              data: data,
              timestamp: new Date()
            });
            
            return originalSend.apply(this, arguments);
          };
        }
        
        return xhr;
      };
    }

    // 3. 拦截axios（如果存在）
    if (window.axios) {
      this.originalMethods.axios = window.axios;
      const debugger = this;
      
      const axiosInterceptor = function(config) {
        if (debugger.isActive) {
          console.log(`🌐 AXIOS: ${config.method?.toUpperCase() || 'GET'} ${config.url}`);
          
          debugger.allRequests.push({
            type: 'axios',
            method: config.method?.toUpperCase() || 'GET',
            url: config.url,
            data: config.data,
            timestamp: new Date()
          });
        }
        return config;
      };
      
      if (window.axios.interceptors) {
        window.axios.interceptors.request.use(axiosInterceptor);
      }
    }

    // 4. 拦截jQuery.ajax（如果存在）
    if (window.jQuery?.ajax) {
      this.originalMethods.jqueryAjax = window.jQuery.ajax;
      const debugger = this;
      
      window.jQuery.ajax = function(options) {
        if (debugger.isActive) {
          const url = typeof options === 'string' ? options : options.url;
          const method = options.type || options.method || 'GET';
          
          console.log(`🌐 JQUERY: ${method} ${url}`);
          
          debugger.allRequests.push({
            type: 'jquery',
            method: method,
            url: url,
            data: options.data,
            timestamp: new Date()
          });
        }
        
        return debugger.originalMethods.jqueryAjax.apply(this, arguments);
      };
    }
  }

  monitorPage() {
    const debugger = this;
    
    // 监听所有点击事件
    document.addEventListener('click', function(event) {
      if (!debugger.isActive) return;
      
      const target = event.target;
      const text = target.textContent || target.value || target.innerText || '';
      
      if (text.includes('保存') || text.includes('提交') || text.includes('确定')) {
        console.log(`🖱️ 关键按钮点击: ${text}`);
        
        // 延迟检查网络请求
        setTimeout(() => {
          debugger.checkRecentActivity();
        }, 500);
      }
    }, true);

    // 监听表单提交
    document.addEventListener('submit', function(event) {
      if (!debugger.isActive) return;
      
      console.log(`📝 表单提交:`, event.target);
      
      setTimeout(() => {
        debugger.checkRecentActivity();
      }, 500);
    }, true);
  }

  checkRecentActivity() {
    const recent = this.allRequests.filter(req => {
      const timeDiff = Date.now() - new Date(req.timestamp).getTime();
      return timeDiff < 2000; // 2秒内
    });

    if (recent.length > 0) {
      console.log(`📡 最近2秒内的请求 (${recent.length}个):`);
      recent.forEach(req => {
        console.log(`  - ${req.type}: ${req.method} ${req.url}`);
      });
    } else {
      console.log(`⚠️ 最近2秒内没有网络请求`);
      this.diagnoseIssue();
    }
  }

  diagnoseIssue() {
    console.log(`🔍 诊断网络请求问题...`);
    
    // 检查页面框架
    const frameworks = [];
    if (window.Vue) frameworks.push('Vue.js');
    if (window.React) frameworks.push('React');
    if (window.angular) frameworks.push('Angular');
    if (window.jQuery) frameworks.push('jQuery');
    
    console.log(`🎯 检测到的框架: ${frameworks.join(', ') || '无'}`);
    
    // 检查网络库
    const libs = [];
    if (window.axios) libs.push('axios');
    if (window.fetch) libs.push('fetch');
    if (window.XMLHttpRequest) libs.push('XMLHttpRequest');
    
    console.log(`🌐 可用的网络库: ${libs.join(', ')}`);
    
    // 检查可能的API对象
    const apiVars = Object.keys(window).filter(key => 
      key.toLowerCase().includes('api') || 
      key.toLowerCase().includes('request') ||
      key.toLowerCase().includes('service')
    );
    
    if (apiVars.length > 0) {
      console.log(`🔍 可能的API变量: ${apiVars.join(', ')}`);
    }
  }

  restoreOriginalMethods() {
    // 恢复原始方法
    if (this.originalMethods.fetch) {
      window.fetch = this.originalMethods.fetch;
    }
    
    if (this.originalMethods.XMLHttpRequest) {
      window.XMLHttpRequest = this.originalMethods.XMLHttpRequest;
    }
    
    if (this.originalMethods.jqueryAjax) {
      window.jQuery.ajax = this.originalMethods.jqueryAjax;
    }
  }

  analyzeResults() {
    console.log(`\n=== 📊 网络请求分析报告 ===`);
    console.log(`总请求数: ${this.allRequests.length}`);
    
    if (this.allRequests.length === 0) {
      console.log(`❌ 未捕获到任何网络请求`);
      console.log(`\n可能的原因:`);
      console.log(`1. 系统使用WebSocket或其他非HTTP协议`);
      console.log(`2. 请求被其他代码拦截或修改`);
      console.log(`3. 使用了未知的网络库`);
      console.log(`4. 请求是通过iframe或其他域名发送的`);
      return;
    }
    
    // 按类型分组
    const byType = {};
    const byMethod = {};
    const byDomain = {};
    
    this.allRequests.forEach(req => {
      byType[req.type] = (byType[req.type] || 0) + 1;
      byMethod[req.method] = (byMethod[req.method] || 0) + 1;
      
      try {
        const domain = new URL(req.url).hostname;
        byDomain[domain] = (byDomain[domain] || 0) + 1;
      } catch (e) {
        byDomain['[无效URL]'] = (byDomain['[无效URL]'] || 0) + 1;
      }
    });
    
    console.log(`\n按类型分布:`, byType);
    console.log(`按方法分布:`, byMethod);
    console.log(`按域名分布:`, byDomain);
    
    // 显示所有请求
    console.log(`\n📋 所有请求详情:`);
    this.allRequests.forEach((req, index) => {
      console.log(`${index + 1}. [${req.type}] ${req.method} ${req.url}`);
      if (req.data) {
        console.log(`   数据:`, req.data);
      }
    });
  }
}

// 创建全局调试器实例
window.networkDebugger = new NetworkDebugger();

// 便捷命令
window.startNetworkDebug = () => {
  window.networkDebugger.start();
  console.log('🚀 网络调试器已启动，请执行跟进操作...');
};

window.stopNetworkDebug = () => {
  window.networkDebugger.stop();
  console.log('📊 网络调试器已停止，请查看分析报告');
};

console.log('🔧 网络调试工具已加载');
console.log('使用方法:');
console.log('  startNetworkDebug() - 启动调试');
console.log('  stopNetworkDebug() - 停止调试');
