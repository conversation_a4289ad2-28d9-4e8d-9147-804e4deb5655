# 紧急修复指南

## 🚨 当前问题状态

1. **POST模式失败** - JWT认证问题
2. **普通模式可能受影响** - 需要验证

## 🚀 立即解决方案

### 方案1：快速修复JWT（推荐）

**步骤**：
1. 按F12打开控制台
2. 输入：`quickFixJWT()`
3. 按回车执行
4. 按照提示操作

**这个命令会**：
- 自动查找可能的token
- 尝试自动设置认证
- 提供手动获取token的指导

### 方案2：手动获取JWT Token

**步骤**：
1. 打开F12开发者工具
2. 切换到**Network**标签
3. **手动执行一次跟进操作**（点击任意跟进按钮）
4. 在Network面板中找到**followUp**请求
5. 点击该请求，查看**Headers**
6. 找到**Authorization**头，复制token值
7. 在控制台输入：`setAuthToken("复制的token")`

**示例**：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```
复制Bearer后面的部分，然后：
```javascript
setAuthToken("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
```

### 方案3：重置并重新开始

**步骤**：
1. **完全退出浏览器**
2. **重新打开浏览器**
3. **重新登录系统**
4. **刷新页面**
5. **重新加载插件**
6. **测试普通模式**

## 🔧 验证修复结果

### 测试普通模式：
1. 点击插件的"开始运行"
2. 观察是否正常工作
3. 查看日志是否有错误

### 测试POST模式：
1. 在控制台运行：`testPostSubmit()`
2. 或点击插件的"🧪 测试POST"
3. 查看是否成功

## 📋 详细操作步骤（如果不熟悉控制台）

### 打开控制台：
1. 在页面上按**F12**键
2. 点击**Console**标签
3. 在底部输入框中输入命令

### 快速修复JWT：
```
输入：quickFixJWT()
按回车
```

### 查看认证信息：
```
输入：getAuthInfo()
按回车
```

### 手动设置token：
```
输入：setAuthToken("这里粘贴实际的token")
按回车
```

## 🎯 预期结果

### 成功修复后应该看到：
```
✅ 认证token已设置
✅ API提交器已更新，现在可以重新测试POST功能
```

### 测试POST成功后应该看到：
```
🧪 POST测试完成！
结果: ✅ 成功
客户: 滨州张先生
线索ID: 13287394434
```

## 🔍 如果还是不行

### 检查清单：
- [ ] 是否在正确的域名 (audiep.faw-vw.com)
- [ ] 是否已重新登录系统
- [ ] 是否刷新了页面
- [ ] 控制台是否有其他错误信息

### 获取更多信息：
1. 运行：`getAuthInfo()` 查看详细认证信息
2. 运行：`debugAPIRequest()` 查看API调试信息
3. 截图Network面板的请求信息

## 💡 临时解决方案

### 如果POST模式一直不行：
**可以继续使用普通模式**，虽然速度慢一些，但功能完整：

1. 点击插件的"开始运行"
2. 设置等待时间为0.01-0.1秒
3. 可以最小化窗口（已支持后台运行）
4. 观察处理进度

### 普通模式的优势：
- ✅ 不依赖JWT认证
- ✅ 使用页面原有的认证方式
- ✅ 支持后台运行
- ✅ 稳定可靠

## 🚀 下一步计划

### 一旦JWT问题解决：
1. POST模式将恢复正常
2. 可以享受10-60倍效率提升
3. 批量处理将非常快速

### 如果需要进一步帮助：
请提供以下信息：
1. `quickFixJWT()` 的输出结果
2. `getAuthInfo()` 的输出结果
3. Network面板中followUp请求的截图
4. 控制台的错误信息

## 📞 紧急联系

如果按照上述步骤仍然无法解决：
1. **先使用普通模式**继续工作
2. **收集上述调试信息**
3. **提供详细的错误描述**

记住：**普通模式仍然可用**，不会影响您的工作进度！
