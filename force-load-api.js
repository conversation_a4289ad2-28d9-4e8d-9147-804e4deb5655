/**
 * 强制加载API客户端的修复脚本
 * 在浏览器控制台中运行此脚本来强制创建API实例
 */

console.log('🔧 开始强制加载API客户端...');

// 强制创建API客户端类
function createAPIClientClass() {
    if (window.AutoFollowUpAPI) {
        console.log('✅ AutoFollowUpAPI类已存在');
        return true;
    }
    
    console.log('🔄 创建AutoFollowUpAPI类...');
    
    // 直接定义API客户端类
    window.AutoFollowUpAPI = class AutoFollowUpAPI {
        constructor() {
            this.baseURL = 'https://audiep.faw-vw.com';
            this.headers = this.getDefaultHeaders();
            this.isDebugMode = true;
        }
        
        getDefaultHeaders() {
            return {
                'Accept': 'application/json, text/plain, */*',
                'Accept-Encoding': 'gzip, deflate, br, zstd',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                'Referer': 'https://audiep.faw-vw.com/',
                'Origin': 'https://audiep.faw-vw.com',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"Windows"'
            };
        }
        
        getAuthHeaders() {
            const cookies = this.parseCookies(document.cookie);
            
            return {
                'appid': 'cyx',
                'adcroletype': '2',
                'dealercode': cookies.salesCode || 'SA37042',
                'dealerid': '166',
                'employeeid': cookies.employeeNo?.replace('7580290', '') || '119734',
                'jwt': cookies.jwt || '',
                'ownercode': cookies.ownerCode || '7580290',
                'positioncode': '10061008',
                'roletype': '10061008',
                'useragent': 'pc',
                'usercode': cookies.CheckedUsername || 'P377412',
                'userid': cookies.userId || '117089',
                'username': cookies.username || '%E8%83%A5%E8%89%B3%E7%BA%A2'
            };
        }
        
        parseCookies(cookieString) {
            const cookies = {};
            cookieString.split(';').forEach(cookie => {
                const [name, value] = cookie.trim().split('=');
                if (name && value) {
                    cookies[name] = decodeURIComponent(value);
                }
            });
            return cookies;
        }
        
        generateTimestamp() {
            return Date.now().toString();
        }
        
        async getLeadList(params = {}) {
            const defaultParams = {
                guestName: '',
                guestPhone: '',
                state: '',
                provinceCode: '',
                cityCode: '',
                intentionSeriesId: '',
                intentionModelId: '',
                callState: '',
                leadLevel: '',
                leadGrade: '',
                isAddWechat: '',
                pageIndex: 1,
                pageSize: 10,
                touchTypeId: '',
                channelLargeId: '',
                channelSmallId: '',
                actualSourceId: '',
                sort: 'next_follow_time',
                desc: false,
                _t: this.generateTimestamp()
            };
            
            const queryParams = { ...defaultParams, ...params };
            const queryString = new URLSearchParams(queryParams).toString();
            const url = `${this.baseURL}/api/adc/v1/lead/query/today/reply?${queryString}`;
            
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        ...this.headers,
                        ...this.getAuthHeaders()
                    }
                });
                
                const data = await response.json();
                this.log('获取客户列表成功', data);
                return data;
            } catch (error) {
                this.log('获取客户列表失败', error, 'error');
                throw error;
            }
        }
        
        async getLeadDetail(leadId) {
            const url = `${this.baseURL}/api/adc/v1/lead/queryLeadDetail?leadId=${leadId}&_t=${this.generateTimestamp()}`;
            
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        ...this.headers,
                        ...this.getAuthHeaders()
                    }
                });
                
                const data = await response.json();
                this.log('获取客户详情成功', data);
                return data;
            } catch (error) {
                this.log('获取客户详情失败', error, 'error');
                throw error;
            }
        }
        
        async submitFollowUp(followUpData) {
            const url = `${this.baseURL}/api/adc/v1/lead/followUp?_t=${this.generateTimestamp()}`;
            
            const defaultData = {
                actualBuyer: "",
                actualBuyerPhone: "",
                bigState: "有效",
                bigStateId: 0,
                buyCarDate: this.formatDateTime(new Date()),
                competition: "",
                consultant: "胥艳红",
                consultantId: "117089",
                currentCarFocus: "",
                customerFocus: "",
                failReason: "",
                followMethod: "电话沟通",
                intentionModel: "",
                intentionModelId: "",
                invalidReason: "",
                isFinancial: "",
                isInvalid: 0,
                isSleep: "",
                isTestDrive: "",
                level: "2_B（30天内跟进）_720",
                levelId: "2",
                levelName: "B（30天内跟进）",
                nextFollowTime: this.formatDateTime(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)),
                nextState: 201,
                policyFocus: "",
                purchaseBudget: "",
                purchaseType: "",
                recomender: "",
                recomenderChassisNo: "",
                recomenderLicensePlate: "",
                recomenderPhone: "",
                recommenderRegisteredTime: "",
                remark: "自动跟进记录",
                replacementType: 1,
                state: 201,
                stateName: "再次待跟进",
                vehicleChassisNo: "",
                vehicleConfig: ""
            };
            
            const payload = { ...defaultData, ...followUpData };
            
            try {
                this.log('准备提交跟进记录', payload);
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        ...this.headers,
                        ...this.getAuthHeaders(),
                        'Content-Length': JSON.stringify(payload).length.toString()
                    },
                    body: JSON.stringify(payload)
                });
                
                const data = await response.json();
                this.log('提交跟进记录成功', data);
                return data;
            } catch (error) {
                this.log('提交跟进记录失败', error, 'error');
                throw error;
            }
        }
        
        formatDateTime(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');
            
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }
        
        log(message, data = null, level = 'info') {
            if (!this.isDebugMode) return;
            
            const timestamp = new Date().toLocaleString();
            const prefix = level === 'error' ? '❌' : level === 'warn' ? '⚠️' : '✅';
            
            console.log(`${prefix} [API客户端] ${timestamp} - ${message}`);
            if (data) {
                console.log(data);
            }
        }
        
        setDebugMode(enabled) {
            this.isDebugMode = enabled;
        }
    };
    
    console.log('✅ AutoFollowUpAPI类创建成功');
    return true;
}

// 强制创建API客户端实例
function createAPIClientInstance() {
    if (window.apiClient) {
        console.log('✅ apiClient实例已存在');
        return true;
    }
    
    try {
        window.apiClient = new window.AutoFollowUpAPI();
        console.log('✅ apiClient实例创建成功');
        return true;
    } catch (error) {
        console.error('❌ apiClient实例创建失败:', error);
        return false;
    }
}

// 强制创建自动跟进处理器类
function createProcessorClass() {
    if (window.AutoFollowProcessor) {
        console.log('✅ AutoFollowProcessor类已存在');
        return true;
    }
    
    console.log('🔄 创建AutoFollowProcessor类...');
    
    window.AutoFollowProcessor = class AutoFollowProcessor {
        constructor() {
            this.apiClient = null;
            this.isRunning = false;
            this.processedLeads = new Set();
            this.config = {
                batchSize: 5,
                delayBetweenRequests: 2000,
                maxRetries: 3,
                autoRemark: true,
                followUpMessages: [
                    "客户您好，我是您的专属顾问，有什么可以帮助您的吗？",
                    "感谢您对我们产品的关注，请问您还有其他疑问吗？",
                    "您好，根据您的需求，我为您推荐几款热门车型",
                    "客户您好，我们最近有优惠活动，您可以了解一下",
                    "感谢您的咨询，请问您方便到店详细了解吗？"
                ]
            };
        }
        
        getAPIClient() {
            if (!this.apiClient) {
                if (window.apiClient) {
                    this.apiClient = window.apiClient;
                } else if (window.AutoFollowUpAPI) {
                    this.apiClient = new window.AutoFollowUpAPI();
                } else {
                    throw new Error('API客户端未加载');
                }
            }
            return this.apiClient;
        }
        
        async startAutoFollow(options = {}) {
            if (this.isRunning) {
                this.log('自动跟进已在运行中', null, 'warn');
                return;
            }
            
            this.isRunning = true;
            this.config = { ...this.config, ...options };
            
            try {
                this.log('🚀 开始自动跟进流程');
                
                const leadList = await this.getLeadsToFollow();
                
                if (!leadList || !leadList.data || !leadList.data.pageData) {
                    this.log('未找到待跟进客户', null, 'warn');
                    return;
                }
                
                const leads = leadList.data.pageData;
                this.log(`📋 找到 ${leads.length} 个待跟进客户`);
                
                await this.processBatch(leads);
                
                this.log('✅ 自动跟进流程完成');
                
            } catch (error) {
                this.log('❌ 自动跟进流程出错', error, 'error');
            } finally {
                this.isRunning = false;
            }
        }
        
        async getLeadsToFollow() {
            try {
                const apiClient = this.getAPIClient();
                const params = {
                    state: '201',
                    pageSize: this.config.batchSize
                };
                
                return await apiClient.getLeadList(params);
            } catch (error) {
                this.log('获取客户列表失败', error, 'error');
                throw error;
            }
        }
        
        async processBatch(leads) {
            for (let i = 0; i < leads.length; i++) {
                const lead = leads[i];
                
                if (this.processedLeads.has(lead.leadId)) {
                    this.log(`⏭️ 跳过已处理客户: ${lead.guestName} (${lead.leadId})`);
                    continue;
                }
                
                try {
                    this.log(`🔄 处理客户 ${i + 1}/${leads.length}: ${lead.guestName} (${lead.leadId})`);
                    
                    await this.processSingleLead(lead);
                    
                    this.processedLeads.add(lead.leadId);
                    
                    if (i < leads.length - 1) {
                        await this.delay(this.config.delayBetweenRequests);
                    }
                    
                } catch (error) {
                    this.log(`❌ 处理客户失败: ${lead.guestName}`, error, 'error');
                }
            }
        }
        
        async processSingleLead(lead) {
            let retries = 0;
            
            while (retries < this.config.maxRetries) {
                try {
                    const apiClient = this.getAPIClient();
                    
                    const leadDetail = await apiClient.getLeadDetail(lead.leadId);
                    
                    if (!leadDetail || !leadDetail.data) {
                        throw new Error('获取客户详情失败');
                    }
                    
                    const followUpData = this.buildFollowUpData(leadDetail.data);
                    
                    const result = await apiClient.submitFollowUp(followUpData);
                    
                    if (result && result.code === '000000') {
                        this.log(`✅ 客户跟进成功: ${lead.guestName}`);
                        return result;
                    } else {
                        throw new Error(`提交失败: ${result?.description || '未知错误'}`);
                    }
                    
                } catch (error) {
                    retries++;
                    this.log(`⚠️ 第${retries}次尝试失败: ${error.message}`, null, 'warn');
                    
                    if (retries >= this.config.maxRetries) {
                        throw error;
                    }
                    
                    await this.delay(1000 * retries);
                }
            }
        }
        
        buildFollowUpData(leadData) {
            const apiClient = this.getAPIClient();
            const now = new Date();
            const nextFollowTime = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
            
            return {
                leadId: leadData.leadId,
                userName: leadData.userName,
                userMobile: leadData.userMobile,
                intentionSeries: `${leadData.intentionSeriesId}_${leadData.intentionSeriesName}`,
                intentionSeriesId: leadData.intentionSeriesId,
                intentionSeriesName: leadData.intentionSeriesName,
                consultant: leadData.consultant,
                consultantId: leadData.consultantId,
                remark: this.generateRemark(leadData),
                nextFollowTime: apiClient.formatDateTime(nextFollowTime),
                buyCarDate: apiClient.formatDateTime(now),
                level: "2_B（30天内跟进）_720",
                levelId: "2",
                levelName: "B（30天内跟进）"
            };
        }
        
        generateRemark(leadData) {
            if (!this.config.autoRemark) {
                return "自动跟进记录";
            }
            
            const messages = this.config.followUpMessages;
            const randomMessage = messages[Math.floor(Math.random() * messages.length)];
            
            const timestamp = new Date().toLocaleString();
            const carInfo = leadData.intentionSeriesName ? `关注车型：${leadData.intentionSeriesName}` : '';
            
            return `${randomMessage} ${carInfo} [自动跟进 ${timestamp}]`;
        }
        
        stop() {
            this.isRunning = false;
            this.log('🛑 自动跟进已停止');
        }
        
        getStats() {
            return {
                isRunning: this.isRunning,
                processedCount: this.processedLeads.size,
                processedLeads: Array.from(this.processedLeads)
            };
        }
        
        reset() {
            this.processedLeads.clear();
            this.log('🔄 处理记录已重置');
        }
        
        async delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
        
        log(message, data = null, level = 'info') {
            const timestamp = new Date().toLocaleString();
            const prefix = level === 'error' ? '❌' : level === 'warn' ? '⚠️' : '✅';
            
            console.log(`${prefix} [自动跟进] ${timestamp} - ${message}`);
            if (data) {
                console.log(data);
            }
            
            if (window.Statistics) {
                window.Statistics.addLog(message, level);
            }
        }
        
        setConfig(newConfig) {
            this.config = { ...this.config, ...newConfig };
            this.log('⚙️ 配置已更新', this.config);
        }
    };
    
    console.log('✅ AutoFollowProcessor类创建成功');
    return true;
}

// 强制创建自动跟进处理器实例
function createProcessorInstance() {
    if (window.autoFollowProcessor) {
        console.log('✅ autoFollowProcessor实例已存在');
        return true;
    }
    
    try {
        window.autoFollowProcessor = new window.AutoFollowProcessor();
        console.log('✅ autoFollowProcessor实例创建成功');
        return true;
    } catch (error) {
        console.error('❌ autoFollowProcessor实例创建失败:', error);
        return false;
    }
}

// 执行强制加载
async function forceLoadAll() {
    console.log('🚀 开始强制加载所有API组件...');
    console.log('='.repeat(50));
    
    const results = {
        apiClass: createAPIClientClass(),
        apiInstance: createAPIClientInstance(),
        processorClass: createProcessorClass(),
        processorInstance: createProcessorInstance()
    };
    
    console.log('\n📊 强制加载结果:');
    Object.entries(results).forEach(([key, value]) => {
        const status = value ? '✅' : '❌';
        const name = key.replace(/([A-Z])/g, ' $1').toLowerCase();
        console.log(`${status} ${name}: ${value ? '成功' : '失败'}`);
    });
    
    const allSuccess = Object.values(results).every(Boolean);
    
    if (allSuccess) {
        console.log('\n🎉 所有组件强制加载成功！');
        
        // 通知插件
        if (window.Statistics) {
            window.Statistics.addLog('🚀 POST提交功能已就绪');
        }
        
        // 测试API功能
        try {
            const testResult = await window.apiClient.getLeadList({ pageSize: 1 });
            if (testResult && testResult.code === '000000') {
                console.log('✅ API功能测试成功');
                if (window.Statistics) {
                    window.Statistics.addLog('✅ API功能测试成功');
                }
            }
        } catch (error) {
            console.log('⚠️ API功能测试失败:', error.message);
        }
        
        return true;
    } else {
        console.log('\n❌ 部分组件加载失败');
        return false;
    }
}

// 导出函数
window.forceLoadAPI = {
    forceLoadAll,
    createAPIClientClass,
    createAPIClientInstance,
    createProcessorClass,
    createProcessorInstance
};

// 自动执行
console.log('🔧 强制加载API工具已准备就绪');
console.log('💡 使用方法: forceLoadAll()');

// 延迟自动执行
setTimeout(() => {
    console.log('\n🔄 自动开始强制加载...');
    forceLoadAll();
}, 500);
