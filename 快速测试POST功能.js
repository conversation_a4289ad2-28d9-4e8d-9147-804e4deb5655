/**
 * 快速测试POST批量提交功能
 * 在浏览器控制台中运行此脚本来验证功能
 */

// 测试配置
const TEST_CONFIG = {
    verbose: true,  // 详细日志
    testDelay: 1000 // 测试间隔
};

/**
 * 测试日志函数
 */
function testLog(message, level = 'info') {
    const timestamp = new Date().toLocaleString();
    const prefix = level === 'error' ? '❌' : level === 'success' ? '✅' : level === 'warn' ? '⚠️' : 'ℹ️';
    console.log(`${prefix} [快速测试] ${timestamp} - ${message}`);
}

/**
 * 延迟函数
 */
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 检查插件是否已加载
 */
function checkExtensionLoaded() {
    testLog('检查插件加载状态...');
    
    // 检查content script是否注入
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        testLog('✅ Chrome扩展API可用', 'success');
        return true;
    } else {
        testLog('❌ Chrome扩展API不可用', 'error');
        return false;
    }
}

/**
 * 检查API客户端是否加载
 */
function checkAPIClient() {
    testLog('检查API客户端...');
    
    if (window.AutoFollowUpAPI && window.apiClient) {
        testLog('✅ API客户端已加载', 'success');
        return true;
    } else {
        testLog('❌ API客户端未加载', 'error');
        testLog('提示: 请确保在CRM页面中运行，并且插件已正确安装', 'warn');
        return false;
    }
}

/**
 * 检查自动跟进处理器
 */
function checkAutoProcessor() {
    testLog('检查自动跟进处理器...');
    
    if (window.AutoFollowProcessor && window.autoFollowProcessor) {
        testLog('✅ 自动跟进处理器已加载', 'success');
        return true;
    } else {
        testLog('❌ 自动跟进处理器未加载', 'error');
        return false;
    }
}

/**
 * 测试API连接
 */
async function testAPIConnection() {
    testLog('测试API连接...');
    
    try {
        // 测试获取客户列表
        const result = await window.apiClient.getLeadList({
            pageSize: 1
        });
        
        if (result && result.code === '000000') {
            const totalCount = result.data?.totalCount || 0;
            testLog(`✅ API连接成功，找到${totalCount}条客户记录`, 'success');
            return true;
        } else {
            testLog(`❌ API返回错误: ${result?.description || '未知错误'}`, 'error');
            return false;
        }
    } catch (error) {
        testLog(`❌ API连接失败: ${error.message}`, 'error');
        return false;
    }
}

/**
 * 检查页面环境
 */
function checkPageEnvironment() {
    testLog('检查页面环境...');
    
    const url = window.location.href;
    const domain = window.location.hostname;
    
    testLog(`当前页面: ${url}`);
    testLog(`域名: ${domain}`);
    
    if (domain.includes('audiep.faw-vw.com')) {
        testLog('✅ 在正确的CRM域名下', 'success');
        return true;
    } else {
        testLog('⚠️ 不在CRM域名下，功能可能无法正常工作', 'warn');
        return false;
    }
}

/**
 * 检查认证状态
 */
function checkAuthStatus() {
    testLog('检查认证状态...');
    
    const cookies = document.cookie;
    const hasJWT = cookies.includes('jwt=');
    const hasUserId = cookies.includes('userId=');
    
    if (hasJWT && hasUserId) {
        testLog('✅ 检测到认证信息', 'success');
        return true;
    } else {
        testLog('⚠️ 未检测到完整认证信息，请确保已登录', 'warn');
        return false;
    }
}

/**
 * 检查插件界面元素
 */
function checkUIElements() {
    testLog('检查插件界面元素...');
    
    // 这个测试只能在popup页面中运行
    if (window.location.href.includes('popup.html')) {
        const postSubmitBtn = document.getElementById('postSubmitBtn');
        const testPostBtn = document.getElementById('testPostBtn');
        
        if (postSubmitBtn && testPostBtn) {
            testLog('✅ 插件界面元素正常', 'success');
            return true;
        } else {
            testLog('❌ 插件界面元素缺失', 'error');
            return false;
        }
    } else {
        testLog('ℹ️ 不在插件界面中，跳过UI检查');
        return true;
    }
}

/**
 * 运行完整测试
 */
async function runQuickTest() {
    testLog('🚀 开始快速测试POST批量提交功能');
    testLog('='.repeat(50));
    
    const tests = [
        { name: '插件加载检查', func: checkExtensionLoaded },
        { name: '页面环境检查', func: checkPageEnvironment },
        { name: '认证状态检查', func: checkAuthStatus },
        { name: 'API客户端检查', func: checkAPIClient },
        { name: '自动跟进处理器检查', func: checkAutoProcessor },
        { name: 'UI元素检查', func: checkUIElements }
    ];
    
    const results = [];
    let passCount = 0;
    
    for (let i = 0; i < tests.length; i++) {
        const test = tests[i];
        testLog(`\n📋 执行测试 ${i + 1}/${tests.length}: ${test.name}`);
        
        try {
            const result = test.func();
            const success = result === true;
            
            results.push({
                name: test.name,
                success: success,
                result: result
            });
            
            if (success) {
                passCount++;
            }
            
            await delay(TEST_CONFIG.testDelay);
        } catch (error) {
            testLog(`❌ 测试异常: ${error.message}`, 'error');
            results.push({
                name: test.name,
                success: false,
                error: error.message
            });
        }
    }
    
    // API连接测试（需要异步）
    if (window.apiClient) {
        testLog(`\n📋 执行测试 ${tests.length + 1}/${tests.length + 1}: API连接测试`);
        try {
            const apiResult = await testAPIConnection();
            results.push({
                name: 'API连接测试',
                success: apiResult,
                result: apiResult
            });
            if (apiResult) passCount++;
        } catch (error) {
            testLog(`❌ API测试异常: ${error.message}`, 'error');
            results.push({
                name: 'API连接测试',
                success: false,
                error: error.message
            });
        }
    }
    
    // 输出测试总结
    testLog('\n📊 测试总结报告');
    testLog('='.repeat(50));
    
    const totalTests = results.length;
    const successRate = totalTests > 0 ? ((passCount / totalTests) * 100).toFixed(1) : 0;
    
    testLog(`总测试数: ${totalTests}`);
    testLog(`通过: ${passCount}`);
    testLog(`失败: ${totalTests - passCount}`);
    testLog(`成功率: ${successRate}%`);
    
    testLog('\n📋 详细结果:');
    results.forEach((result, index) => {
        const status = result.success ? '✅' : '❌';
        testLog(`${status} ${index + 1}. ${result.name}`);
        if (result.error) {
            testLog(`   错误: ${result.error}`);
        }
    });
    
    // 给出建议
    testLog('\n💡 使用建议:');
    if (passCount === totalTests) {
        testLog('🎉 所有测试通过！POST批量提交功能可以正常使用', 'success');
        testLog('建议: 可以开始使用POST批量提交功能');
    } else if (passCount >= totalTests * 0.8) {
        testLog('⚠️ 大部分测试通过，功能基本可用', 'warn');
        testLog('建议: 可以尝试使用，但请注意观察运行日志');
    } else {
        testLog('❌ 多项测试失败，建议检查环境配置', 'error');
        testLog('建议: 请按照使用指南检查环境和配置');
    }
    
    return {
        totalTests,
        passCount,
        successRate,
        results
    };
}

/**
 * 简化版测试（仅检查核心功能）
 */
async function runSimpleTest() {
    testLog('🏃‍♂️ 运行简化测试...');
    
    let score = 0;
    const maxScore = 4;
    
    // 检查基本环境
    if (window.location.hostname.includes('audiep.faw-vw.com')) {
        testLog('✅ 在CRM域名下');
        score++;
    }
    
    // 检查API客户端
    if (window.apiClient) {
        testLog('✅ API客户端已加载');
        score++;
    }
    
    // 检查自动跟进处理器
    if (window.autoFollowProcessor) {
        testLog('✅ 自动跟进处理器已加载');
        score++;
    }
    
    // 检查认证
    if (document.cookie.includes('jwt=')) {
        testLog('✅ 检测到认证信息');
        score++;
    }
    
    const percentage = (score / maxScore * 100).toFixed(0);
    testLog(`📊 简化测试完成: ${score}/${maxScore} (${percentage}%)`);
    
    if (score === maxScore) {
        testLog('🎉 简化测试全部通过！', 'success');
    } else if (score >= 3) {
        testLog('⚠️ 基本功能可用', 'warn');
    } else {
        testLog('❌ 环境配置有问题', 'error');
    }
    
    return { score, maxScore, percentage };
}

// 导出测试函数
window.quickTestPOST = {
    runQuickTest,
    runSimpleTest,
    checkAPIClient,
    checkAutoProcessor,
    testAPIConnection
};

// 自动运行简化测试
if (typeof window !== 'undefined') {
    console.log('🧪 POST功能快速测试脚本已加载');
    console.log('💡 使用方法:');
    console.log('   - runQuickTest() : 运行完整测试');
    console.log('   - runSimpleTest() : 运行简化测试');
    console.log('   - window.quickTestPOST : 访问所有测试函数');
    
    // 延迟执行简化测试，给页面加载时间
    setTimeout(() => {
        if (window.location.hostname.includes('audiep.faw-vw.com')) {
            runSimpleTest();
        }
    }, 2000);
}
