/**
 * 调试插件连接问题的脚本
 * 在浏览器控制台中运行此脚本来诊断问题
 */

console.log('🔧 开始调试插件连接问题...');

// 1. 检查插件是否已安装
function checkExtensionInstalled() {
    console.log('\n📋 1. 检查插件安装状态');
    
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id) {
        console.log('✅ Chrome扩展API可用');
        console.log('📦 插件ID:', chrome.runtime.id);
        return true;
    } else {
        console.log('❌ Chrome扩展API不可用');
        console.log('💡 请确保：');
        console.log('   - 插件已正确安装');
        console.log('   - 在支持扩展的页面中运行');
        return false;
    }
}

// 2. 检查content script是否注入
function checkContentScript() {
    console.log('\n📋 2. 检查Content Script状态');
    
    // 检查是否有Statistics对象
    if (typeof window.Statistics !== 'undefined') {
        console.log('✅ Statistics对象存在');
    } else {
        console.log('❌ Statistics对象不存在');
    }
    
    // 检查是否有API客户端
    if (typeof window.AutoFollowUpAPI !== 'undefined') {
        console.log('✅ AutoFollowUpAPI类已加载');
    } else {
        console.log('❌ AutoFollowUpAPI类未加载');
    }
    
    if (typeof window.apiClient !== 'undefined') {
        console.log('✅ apiClient实例存在');
    } else {
        console.log('❌ apiClient实例不存在');
    }
    
    // 检查是否有自动跟进处理器
    if (typeof window.AutoFollowProcessor !== 'undefined') {
        console.log('✅ AutoFollowProcessor类已加载');
    } else {
        console.log('❌ AutoFollowProcessor类未加载');
    }
    
    if (typeof window.autoFollowProcessor !== 'undefined') {
        console.log('✅ autoFollowProcessor实例存在');
    } else {
        console.log('❌ autoFollowProcessor实例不存在');
    }
}

// 3. 检查页面环境
function checkPageEnvironment() {
    console.log('\n📋 3. 检查页面环境');
    
    console.log('🌐 当前URL:', window.location.href);
    console.log('🏠 域名:', window.location.hostname);
    
    if (window.location.hostname.includes('audiep.faw-vw.com')) {
        console.log('✅ 在正确的CRM域名下');
    } else {
        console.log('⚠️ 不在CRM域名下');
        console.log('💡 建议在 audiep.faw-vw.com 域名下使用');
    }
    
    // 检查登录状态
    const cookies = document.cookie;
    if (cookies.includes('jwt=') && cookies.includes('userId=')) {
        console.log('✅ 检测到登录信息');
    } else {
        console.log('⚠️ 未检测到完整登录信息');
        console.log('💡 请确保已登录CRM系统');
    }
}

// 4. 测试消息传递
function testMessagePassing() {
    console.log('\n📋 4. 测试消息传递');
    
    return new Promise((resolve) => {
        if (typeof chrome === 'undefined' || !chrome.runtime) {
            console.log('❌ Chrome扩展API不可用');
            resolve(false);
            return;
        }
        
        // 测试发送消息到background script
        try {
            chrome.runtime.sendMessage({
                action: 'test',
                from: 'debug'
            }, (response) => {
                if (chrome.runtime.lastError) {
                    console.log('❌ 消息传递失败:', chrome.runtime.lastError.message);
                    resolve(false);
                } else {
                    console.log('✅ 消息传递成功:', response);
                    resolve(true);
                }
            });
        } catch (error) {
            console.log('❌ 消息传递异常:', error.message);
            resolve(false);
        }
    });
}

// 5. 手动注入content script
function manualInjectContentScript() {
    console.log('\n📋 5. 尝试手动注入Content Script');
    
    if (typeof chrome === 'undefined' || !chrome.runtime) {
        console.log('❌ 无法手动注入，Chrome扩展API不可用');
        return false;
    }
    
    try {
        // 创建script标签加载content script
        const script = document.createElement('script');
        script.src = chrome.runtime.getURL('content.js');
        script.onload = () => {
            console.log('✅ Content Script手动注入成功');
        };
        script.onerror = () => {
            console.log('❌ Content Script手动注入失败');
        };
        document.head.appendChild(script);
        
        console.log('🔄 正在尝试手动注入Content Script...');
        return true;
    } catch (error) {
        console.log('❌ 手动注入失败:', error.message);
        return false;
    }
}

// 6. 检查manifest权限
function checkManifestPermissions() {
    console.log('\n📋 6. 检查Manifest权限');
    
    if (typeof chrome === 'undefined' || !chrome.runtime) {
        console.log('❌ 无法检查权限');
        return false;
    }
    
    // 检查基本权限
    const manifest = chrome.runtime.getManifest();
    console.log('📄 Manifest版本:', manifest.manifest_version);
    console.log('📋 权限列表:', manifest.permissions);
    console.log('🌐 主机权限:', manifest.host_permissions);
    
    return true;
}

// 7. 提供修复建议
function provideFixes() {
    console.log('\n💡 修复建议:');
    console.log('');
    console.log('如果遇到 "Could not establish connection" 错误：');
    console.log('');
    console.log('1. 🔄 重新加载插件');
    console.log('   - 打开 chrome://extensions/');
    console.log('   - 找到"自动跟进助手"');
    console.log('   - 点击刷新按钮');
    console.log('');
    console.log('2. 🔄 刷新页面');
    console.log('   - 按 F5 或 Ctrl+R 刷新当前页面');
    console.log('   - 确保在CRM系统页面中使用');
    console.log('');
    console.log('3. 🔧 手动注入Content Script');
    console.log('   - 运行: manualInjectContentScript()');
    console.log('');
    console.log('4. 📋 检查控制台错误');
    console.log('   - 查看浏览器控制台是否有错误信息');
    console.log('   - 查看插件的background页面控制台');
    console.log('');
    console.log('5. 🔄 重启浏览器');
    console.log('   - 完全关闭浏览器后重新打开');
}

// 运行完整诊断
async function runFullDiagnosis() {
    console.log('🚀 开始完整诊断...');
    console.log('='.repeat(50));
    
    const results = {
        extensionInstalled: checkExtensionInstalled(),
        pageEnvironment: checkPageEnvironment(),
        manifestPermissions: checkManifestPermissions()
    };
    
    checkContentScript();
    
    // 测试消息传递
    console.log('\n⏳ 测试消息传递...');
    results.messagePassing = await testMessagePassing();
    
    console.log('\n📊 诊断结果总结:');
    console.log('='.repeat(50));
    
    Object.entries(results).forEach(([key, value]) => {
        const status = value ? '✅' : '❌';
        const name = key.replace(/([A-Z])/g, ' $1').toLowerCase();
        console.log(`${status} ${name}: ${value ? '正常' : '异常'}`);
    });
    
    const passCount = Object.values(results).filter(Boolean).length;
    const totalCount = Object.keys(results).length;
    
    console.log(`\n📈 总体状态: ${passCount}/${totalCount} 项正常`);
    
    if (passCount === totalCount) {
        console.log('🎉 所有检查都通过！插件应该可以正常工作');
    } else {
        console.log('⚠️ 发现问题，请查看修复建议');
        provideFixes();
    }
    
    return results;
}

// 导出函数到全局
window.debugConnection = {
    runFullDiagnosis,
    checkExtensionInstalled,
    checkContentScript,
    checkPageEnvironment,
    testMessagePassing,
    manualInjectContentScript,
    checkManifestPermissions,
    provideFixes
};

// 自动运行诊断
console.log('🔧 调试工具已加载');
console.log('💡 使用方法:');
console.log('   - runFullDiagnosis() : 运行完整诊断');
console.log('   - manualInjectContentScript() : 手动注入Content Script');
console.log('   - window.debugConnection : 访问所有调试函数');

// 延迟自动运行
setTimeout(() => {
    console.log('\n🔄 自动开始诊断...');
    runFullDiagnosis();
}, 1000);
