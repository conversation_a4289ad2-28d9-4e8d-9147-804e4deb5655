/**
 * 自动跟进API客户端
 * 基于抓包数据分析实现的POST提交功能
 */

class AutoFollowUpAPI {
  constructor() {
    this.baseURL = 'https://audiep.faw-vw.com';
    this.headers = this.getDefaultHeaders();
    this.isDebugMode = true;
  }

  /**
   * 获取默认请求头
   */
  getDefaultHeaders() {
    return {
      'Accept': 'application/json, text/plain, */*',
      'Accept-Encoding': 'gzip, deflate, br, zstd',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
      'Content-Type': 'application/json',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
      'Referer': 'https://audiep.faw-vw.com/',
      'Origin': 'https://audiep.faw-vw.com',
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'same-origin',
      'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
      'Sec-Ch-Ua-Mobile': '?0',
      'Sec-Ch-Ua-Platform': '"Windows"'
    };
  }

  /**
   * 从页面获取认证信息
   */
  getAuthHeaders() {
    const cookies = this.parseCookies(document.cookie);
    
    return {
      'appid': 'cyx',
      'adcroletype': '2',
      'dealercode': cookies.salesCode || 'SA37042',
      'dealerid': '166',
      'employeeid': cookies.employeeNo?.replace('7580290', '') || '119734',
      'jwt': cookies.jwt || '',
      'ownercode': cookies.ownerCode || '7580290',
      'positioncode': '10061008',
      'roletype': '10061008',
      'useragent': 'pc',
      'usercode': cookies.CheckedUsername || 'P377412',
      'userid': cookies.userId || '117089',
      'username': cookies.username || '%E8%83%A5%E8%89%B3%E7%BA%A2'
    };
  }

  /**
   * 解析Cookie字符串
   */
  parseCookies(cookieString) {
    const cookies = {};
    cookieString.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name && value) {
        cookies[name] = decodeURIComponent(value);
      }
    });
    return cookies;
  }

  /**
   * 生成时间戳
   */
  generateTimestamp() {
    return Date.now().toString();
  }

  /**
   * 获取客户列表
   */
  async getLeadList(params = {}) {
    const defaultParams = {
      guestName: '',
      guestPhone: '',
      state: '',
      provinceCode: '',
      cityCode: '',
      intentionSeriesId: '',
      intentionModelId: '',
      callState: '',
      leadLevel: '',
      leadGrade: '',
      isAddWechat: '',
      pageIndex: 1,
      pageSize: 10,
      touchTypeId: '',
      channelLargeId: '',
      channelSmallId: '',
      actualSourceId: '',
      sort: 'next_follow_time',
      desc: false,
      _t: this.generateTimestamp()
    };

    const queryParams = { ...defaultParams, ...params };
    const queryString = new URLSearchParams(queryParams).toString();
    const url = `${this.baseURL}/api/adc/v1/lead/query/today/reply?${queryString}`;

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          ...this.headers,
          ...this.getAuthHeaders()
        }
      });

      const data = await response.json();
      this.log('获取客户列表成功', data);
      return data;
    } catch (error) {
      this.log('获取客户列表失败', error, 'error');
      throw error;
    }
  }

  /**
   * 获取客户详情
   */
  async getLeadDetail(leadId) {
    const url = `${this.baseURL}/api/adc/v1/lead/queryLeadDetail?leadId=${leadId}&_t=${this.generateTimestamp()}`;

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          ...this.headers,
          ...this.getAuthHeaders()
        }
      });

      const data = await response.json();
      this.log('获取客户详情成功', data);
      return data;
    } catch (error) {
      this.log('获取客户详情失败', error, 'error');
      throw error;
    }
  }

  /**
   * 提交跟进记录
   */
  async submitFollowUp(followUpData) {
    const url = `${this.baseURL}/api/adc/v1/lead/followUp?_t=${this.generateTimestamp()}`;

    // 构建默认的跟进数据结构
    const defaultData = {
      actualBuyer: "",
      actualBuyerPhone: "",
      bigState: "有效",
      bigStateId: 0,
      buyCarDate: this.formatDateTime(new Date()),
      competition: "",
      consultant: "胥艳红",
      consultantId: "117089",
      currentCarFocus: "",
      customerFocus: "",
      failReason: "",
      followMethod: "电话沟通",
      intentionModel: "",
      intentionModelId: "",
      invalidReason: "",
      isFinancial: "",
      isInvalid: 0,
      isSleep: "",
      isTestDrive: "",
      level: "2_B（30天内跟进）_720",
      levelId: "2",
      levelName: "B（30天内跟进）",
      nextFollowTime: this.formatDateTime(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)), // 30天后
      nextState: 201,
      policyFocus: "",
      purchaseBudget: "",
      purchaseType: "",
      recomender: "",
      recomenderChassisNo: "",
      recomenderLicensePlate: "",
      recomenderPhone: "",
      recommenderRegisteredTime: "",
      remark: "自动跟进记录",
      replacementType: 1,
      state: 201,
      stateName: "再次待跟进",
      vehicleChassisNo: "",
      vehicleConfig: ""
    };

    const payload = { ...defaultData, ...followUpData };

    try {
      this.log('准备提交跟进记录', payload);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          ...this.headers,
          ...this.getAuthHeaders(),
          'Content-Length': JSON.stringify(payload).length.toString()
        },
        body: JSON.stringify(payload)
      });

      const data = await response.json();
      this.log('提交跟进记录成功', data);
      return data;
    } catch (error) {
      this.log('提交跟进记录失败', error, 'error');
      throw error;
    }
  }

  /**
   * 格式化日期时间
   */
  formatDateTime(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  /**
   * 日志输出
   */
  log(message, data = null, level = 'info') {
    if (!this.isDebugMode) return;

    const timestamp = new Date().toLocaleString();
    const prefix = level === 'error' ? '❌' : level === 'warn' ? '⚠️' : '✅';
    
    console.log(`${prefix} [API客户端] ${timestamp} - ${message}`);
    if (data) {
      console.log(data);
    }
  }

  /**
   * 设置调试模式
   */
  setDebugMode(enabled) {
    this.isDebugMode = enabled;
  }
}

// 导出API客户端
window.AutoFollowUpAPI = AutoFollowUpAPI;

// 创建全局实例
try {
  window.apiClient = new AutoFollowUpAPI();
  console.log('✅ [API客户端] 实例创建成功');
} catch (error) {
  console.error('❌ [API客户端] 实例创建失败:', error);
}

console.log('✅ [API客户端] 已加载完成');
