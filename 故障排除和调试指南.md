# 故障排除和调试指南

## 🔍 问题1：POST模式失败分析

### 症状
```
❌ 跟进(手机号) 跟进失败: API错误: undefined
📊 批次完成: 成功 0, 失败 X
```

### 可能原因和解决方案

#### 1. **域名不匹配（最可能）**
**检查方法**：
```javascript
// 在控制台运行
debugAPIRequest()
```

**解决方案**：
- 确保在 `https://audiep.faw-vw.com` 域名下使用
- 如果在其他域名，会出现CORS错误

#### 2. **认证问题**
**检查方法**：
```javascript
// 检查登录状态
console.log('Cookies:', document.cookie);
console.log('当前URL:', window.location.href);
```

**解决方案**：
- 确保已登录系统
- 刷新页面重新登录
- 检查session是否过期

#### 3. **API端点变化**
**检查方法**：
1. 打开F12开发者工具
2. 切换到Network标签
3. 手动执行一次跟进操作
4. 查看实际的API请求URL

**解决方案**：
- 如果API地址变化，需要更新代码中的baseURL

#### 4. **请求参数问题**
**检查方法**：
```javascript
// 查看生成的请求参数
const customers = window.directAPISubmitter.extractCustomerData();
const testData = window.directAPISubmitter.generateFollowUpData(customers[0]);
console.log('请求参数:', testData);
```

## 🔍 问题2：普通模式后台运行

### 已实现的解决方案

#### ✅ **后台运行支持**
- **页面可见性检测** - 自动识别页面是否在后台
- **后台兼容模式** - 减少动画等待，提高效率
- **实时状态提示** - 显示当前运行模式

#### ✅ **优化措施**
- **跳过滚动动画** - 后台运行时直接定位元素
- **减少等待时间** - 后台模式使用更短的等待间隔
- **直接点击方式** - 后台时使用更直接的点击方法

### 使用建议
1. **可以最小化窗口** - 程序会自动切换到后台模式
2. **可以切换到其他窗口** - 不影响运行
3. **观察日志提示** - 会显示"页面已最小化，切换到后台运行模式"

## 🔧 调试工具和命令

### 基础调试命令
```javascript
// 测试数据提取
testDataExtraction()

// 测试POST提交
testPostSubmit()

// 调试API请求信息
debugAPIRequest()

// 启动POST模式
startPostMode()
```

### 高级调试命令
```javascript
// 查看提取的原始数据
window.directAPISubmitter.extractCustomerData()

// 查看API请求参数
const customer = {leadId: "123", userName: "测试", userMobile: "13800138000"};
window.directAPISubmitter.generateFollowUpData(customer)

// 检查页面可见性
console.log('页面是否隐藏:', document.hidden);

// 检查运行状态
console.log('是否正在运行:', isRunning);
```

### 网络调试
```javascript
// 检查当前域名
console.log('当前域名:', window.location.origin);

// 检查cookies
console.log('Cookies:', document.cookie);

// 检查API基础URL
console.log('API基础URL:', window.directAPISubmitter.baseURL);
```

## 🛠️ 常见问题解决

### 数据提取问题

#### 症状：提取到的姓名都是"跟进"
**原因**：页面结构变化，提取逻辑识别错误
**解决**：
1. 检查页面HTML结构
2. 手动查看表格列的内容
3. 可能需要调整提取选择器

#### 症状：提取到0条数据
**原因**：页面未完全加载或选择器不匹配
**解决**：
1. 刷新页面，等待完全加载
2. 滚动页面确保数据显示
3. 检查是否有客户记录

### API调用问题

#### 症状：所有请求都失败
**检查清单**：
- [ ] 是否在正确的域名 (audiep.faw-vw.com)
- [ ] 是否已登录系统
- [ ] 网络连接是否正常
- [ ] 浏览器控制台是否有错误

#### 症状：部分请求失败
**可能原因**：
- 网络不稳定
- 服务器限流
- 数据格式问题

**解决方案**：
- 减少并发数量
- 增加请求间隔
- 检查失败记录的数据

## 📊 性能优化建议

### 普通模式优化
1. **使用后台运行** - 最小化窗口提高效率
2. **调整等待时间** - 设置为0.01-0.1秒
3. **启用智能表单填充** - 减少手动操作

### POST模式优化
1. **先测试再批量** - 确保API调用正常
2. **分批处理** - 每次处理50-100条记录
3. **监控成功率** - 如果成功率低于90%需要检查

## 🚨 紧急故障处理

### 如果程序卡死
1. **停止运行** - 点击插件的"停止"按钮
2. **刷新页面** - 重新加载页面和插件
3. **检查日志** - 查看最后的错误信息

### 如果数据丢失
1. **检查统计** - 插件会保存历史统计
2. **查看日志** - 所有操作都有详细日志
3. **手动验证** - 在系统中检查实际跟进记录

### 如果API调用异常
1. **立即停止** - 避免产生错误数据
2. **检查登录** - 重新登录系统
3. **手动测试** - 先手动执行一次跟进确认系统正常

## 💡 最佳实践

### 使用前准备
1. **确保网络稳定**
2. **确认登录状态**
3. **先进行小批量测试**

### 运行中监控
1. **观察日志输出**
2. **检查成功率**
3. **注意错误提示**

### 运行后验证
1. **检查统计结果**
2. **抽查实际记录**
3. **保存处理报告**

## 🔄 版本更新说明

### 当前版本改进
- ✅ **增强POST模式调试** - 详细的API调用日志
- ✅ **后台运行支持** - 页面最小化仍可正常工作
- ✅ **改进数据提取** - 更准确的姓名识别
- ✅ **完善错误处理** - 更详细的错误信息

### 下次更新计划
- 🔄 **自动重试机制** - 失败请求自动重试
- 🔄 **智能并发控制** - 根据成功率动态调整
- 🔄 **数据验证增强** - 提交前验证数据完整性
